<?php
// Simple test for AJAX cleanup
require_once 'includes/config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $used_type = (int)$_POST['used_type'];
        
        // Test database connection
        if (!isset($conn) || $conn->connect_error) {
            throw new Exception('Database connection failed');
        }
        
        // Test query
        $result = $conn->query("SELECT COUNT(*) as count FROM race_tokens WHERE used = $used_type");
        if (!$result) {
            throw new Exception('Query failed: ' . $conn->error);
        }
        
        $count = $result->fetch_assoc()['count'];
        
        echo json_encode([
            'success' => true,
            'message' => "Found $count stakes with used = $used_type",
            'count' => $count
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX</title>
</head>
<body>
    <h2>Test AJAX Cleanup</h2>
    <button onclick="testAjax(2)">Test used = 2</button>
    <button onclick="testAjax(3)">Test used = 3</button>
    <div id="result"></div>

    <script>
    function testAjax(usedType) {
        const formData = new FormData();
        formData.append('used_type', usedType);
        
        fetch('test_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = 
                '<p>Success: ' + data.success + '</p>' +
                '<p>Message: ' + data.message + '</p>';
        })
        .catch(error => {
            document.getElementById('result').innerHTML = 
                '<p style="color:red">Error: ' + error + '</p>';
        });
    }
    </script>
</body>
</html>
