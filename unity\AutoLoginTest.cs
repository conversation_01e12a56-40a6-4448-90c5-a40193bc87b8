using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;
using System;

/// <summary>
/// Direct auto login script that sends login request directly to server
/// </summary>
public class AutoLoginTest : MonoBehaviour
{
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/login.php";
    [SerializeField] private GameObject sessionManagerPrefab;

    private void Start()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
            Debug.Log("SessionManager instantiated from AutoLoginTest");
        }

        // Start auto login process
        StartCoroutine(DirectLoginCoroutine());
    }

    private IEnumerator DirectLoginCoroutine()
    {
        // Create form data with test credentials
        Dictionary<string, string> formData = new Dictionary<string, string>
        {
            { "username", "aaa" },
            { "password", "321321321" }
        };

        // Create web request
        UnityWebRequest request = UnityWebRequest.Post(apiUrl, formData);
        request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

        Debug.Log("Auto Login Test - Sending login request with username: aaa, password: 321321321");

        // Send request
        yield return request.SendWebRequest();

        // Process response
        try
        {
            string responseText = request.downloadHandler.text;
            Debug.Log($"Auto Login Test - Login response: {responseText}");

            if (!string.IsNullOrEmpty(responseText))
            {
                LoginResponse response = JsonUtility.FromJson<LoginResponse>(responseText);

                if (response != null && response.status == "success")
                {
                    // Store token and key in SessionManager
                    if (SessionManager.Instance != null)
                    {
                        SessionManager.Instance.SetSessionData(response.token, response.key1, response.nonce, "aaa");
                    }
                    else
                    {
                        // Fallback to static variables if SessionManager not available
                        Login.JWT_Token = response.token;
                        Login.Key1 = response.key1;
                        Login.CurrentNonce = response.nonce;
                    }

                    Debug.Log("Auto Login Test - Login successful!");
                    Debug.Log($"Auto Login Test - Token expires in: {response.token_expires_in} seconds ({response.token_expires_in / 3600.0f} hours)");
                    Debug.Log($"Auto Login Test - Token type: {response.token_type}");


                }
                else
                {
                    Debug.LogError("Auto Login Test - Login failed: " + response.message);
                }
            }
            else if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Auto Login Test - Login Error: " + request.error);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Auto Login Test - Error parsing login response: " + ex.Message);
        }
    }

    [Serializable]
    private class LoginResponse
    {
        public string status;
        public string message;
        public string token;
        public string key1;
        public string nonce;
        public int token_expires_in; // 24 hours in seconds
        public string token_type; // "Bearer"
    }
}
