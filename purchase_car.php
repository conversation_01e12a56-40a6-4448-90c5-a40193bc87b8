<?php
/**
 * Purchase Car API
 *
 * This API handles car operations for the game:
 * - Fetching user data (including owned cars)
 * - Purchasing new cars
 * - Activating purchased cars
 *
 * All operations require JWT token authentication
 */

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', 'purchase_car_errors.log');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Disable debug logging to prevent log file growth

// Helper function to safely log debug info
function debug_log($message) {
    // Commenting out the logging to prevent file growth
    // error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, 'purchase_car_debug.log');
}

// Start debugging
// debug_log("API request received: " . json_encode($_POST));

// Check if this is a test request
if (isset($_GET['test']) && $_GET['test'] == '1') {
    // debug_log("Test request received");
    echo json_encode([
        'status' => 'success',
        'message' => 'API is working',
        'time' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Check if getBearerToken function exists
if (!function_exists('getBearerToken')) {
    function getBearerToken() {
        $headers = null;
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }

        if (!empty($headers) && preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
        return null;
    }

    // debug_log("Created getBearerToken function");
}

// Get authorization token
$token = getBearerToken();
// debug_log("Token received: " . ($token ? "Yes" : "No"));

if (!$token) {
    respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
}

// Verify token
$user_id = verifyJWT($token);
// debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

if (!$user_id) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    // debug_log("Extracted user_id from array: " . $user_id);
}

// Check rate limit
if (!checkRateLimit($user_id)) {
    respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

// debug_log("Received input: " . json_encode($input));

// Validate required fields - with 24-hour token system, only key1 is required
if (!isset($input['key1'])) {
    respondJSON(['status' => 'error', 'message' => 'Key1 is required'], 400);
}

$key1 = $input['key1'];
// With 24-hour token system, nonce is optional
$nonce = isset($input['nonce']) ? $input['nonce'] : '';
$operation = isset($input['operation']) ? $input['operation'] : '';
$car_id = isset($input['car_id']) ? $input['car_id'] : '';
// اضافه کردن پارامتر جدید برای تعیین نوع خرید
$use_referral_balance = isset($input['use_referral_balance']) && filter_var($input['use_referral_balance'], FILTER_VALIDATE_BOOLEAN);

// debug_log("Operation: $operation, Car ID: $car_id");

// Normalize operation string to handle case-insensitive comparison
$operation = strtolower($operation);

// Verify Key1
if (!verifyUserKey($user_id, $key1)) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
}

// Verify Nonce if provided (with 24-hour token system, nonce is optional)
if (!empty($nonce)) {
    if (!verifyAndUseNonce($user_id, $nonce)) {
        // Generate a new nonce for the client to use
        $new_nonce = generateNonce($user_id);

        // Return error with the new nonce
        respondJSON([
            'status' => 'error',
            'message' => 'Invalid or used nonce',
            'should_retry' => true,
            'error_code' => 'INVALID_NONCE',
            'nonce' => $new_nonce
        ], 401);
    }
} else {
    // With 24-hour token system, we can proceed without a nonce
    // debug_log("No nonce provided, proceeding with 24-hour token system");
}

// Generate a new nonce for the next request
$new_nonce = generateNonce($user_id);
// debug_log("New nonce generated: $new_nonce");

// Execute the requested operation
switch ($operation) {
    case 'fetchuserdata':
    case 'fetch_user_data':
        fetch_user_data($conn, $user_id, $new_nonce);
        break;

    case 'purchasecar':
    case 'purchase_car':
        if (empty($car_id)) {
            respondJSON([
                'status' => 'error',
                'message' => 'Car ID is required for purchase operation',
                'nonce' => $new_nonce
            ]);
        }
        purchase_car($conn, $user_id, $car_id, $new_nonce, $use_referral_balance);
        break;

    case 'activatecar':
    case 'activate_car':
        if (empty($car_id)) {
            respondJSON([
                'status' => 'error',
                'message' => 'Car ID is required for activation operation',
                'nonce' => $new_nonce
            ]);
        }
        activate_car($conn, $user_id, $car_id, $new_nonce);
        break;

    case 'getallcars':
    case 'get_all_cars':
        get_all_cars($new_nonce);
        break;

    default:
        respondJSON([
            'status' => 'error',
            'message' => 'Invalid operation: ' . $operation,
            'nonce' => $new_nonce
        ], 400);
        break;
}

/**
 * Fetch user data including owned cars
 */
function fetch_user_data($conn, $user_id, $new_nonce) {
    // debug_log("Fetching user data for user ID: $user_id");

    try {
        // Get user data including referral_balance
        $stmt = $conn->prepare("SELECT username, balance, referral_balance FROM users u WHERE u.id = ?");

        if (!$stmt) {
            // debug_log("SQL preparation error: " . $conn->error);
            respondJSON(['status' => 'error', 'message' => 'Database error', 'nonce' => $new_nonce], 500);
        }

        $stmt->bind_param('i', $user_id);
        $result = $stmt->execute();

        if (!$result) {
            // debug_log("SQL execution error: " . $stmt->error);
            respondJSON(['status' => 'error', 'message' => 'Database error', 'nonce' => $new_nonce], 500);
        }

        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // debug_log("User not found");
            respondJSON(['status' => 'error', 'message' => 'User not found', 'nonce' => $new_nonce], 404);
        }

        $user = $result->fetch_assoc();

        // Get user's cars from the user_cars table
        $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $cars_result = $stmt->get_result();

        // Old format (for backward compatibility)
        $cars_data = [];

        // New format - array of car objects
        $user_cars = [];

        while ($car = $cars_result->fetch_assoc()) {
            // Old format
            $cars_data[] = $car['car_id'] . ' ' . $car['is_activated'];

            // New format
            $user_cars[] = [
                'id' => (int)$car['id'],
                'user_id' => $car['user_id'],
                'car_id' => $car['car_id'],
                'is_activated' => (int)$car['is_activated'],
                'purchase_date' => $car['purchase_date']
            ];
        }

        // debug_log("User cars: " . json_encode($user_cars));

        // Generate a unique encryption key for this session
        // ترکیبی از شناسه کاربر، زمان فعلی و یک مقدار تصادفی
        $unique_seed = $user_id . time() . bin2hex(random_bytes(8));
        $encryption_key = hash('sha256', $unique_seed);

        // تولید یک کلید کوتاه‌تر - 32 کاراکتر اول
        $short_encryption_key = substr($encryption_key, 0, 32);

        // debug_log("Generated encryption key for user: $user_id");

        // Return user data with encryption key
        $response = [
            'status' => 'success',
            'user' => [
                'username' => $user['username'],
                'balance' => (float)$user['balance'],
                'referral_balance' => (float)$user['referral_balance'],
                'cars_data' => $cars_data,
                'user_cars' => $user_cars // Add the new user_cars array
            ],
            'encryption_key' => $short_encryption_key,
            'nonce' => $new_nonce
        ];

        respondJSON($response);

    } catch (Exception $e) {
        // debug_log("Exception in fetch_user_data: " . $e->getMessage());
        respondJSON([
            'status' => 'error',
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $new_nonce
        ], 500);
    }
}

/**
 * Purchase a car if user has enough balance
 */
function purchase_car($conn, $user_id, $car_id, $new_nonce, $use_referral_balance = false) {
    // debug_log("Processing car purchase: User=$user_id, Car=$car_id, Use Referral Balance=" . ($use_referral_balance ? "Yes" : "No"));

    try {
        // Start transaction
        $conn->begin_transaction();

        // First, check if the user already owns this car
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_cars WHERE user_id = ? AND car_id = ?");
        $stmt->bind_param('is', $user_id, $car_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $owned = $result->fetch_assoc()['count'] > 0;

        if ($owned) {
            // debug_log("User already owns this car");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'You already own this car',
                'nonce' => $new_nonce
            ], 400);
        }

        // Get car price
        $price = get_car_price($car_id);

        if ($price === false) {
            // debug_log("Invalid car ID: $car_id");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'Invalid car ID',
                'nonce' => $new_nonce
            ], 400);
        }

        // debug_log("Car price: $price");

        // Get user balance and other data - اضافه کردن referral_balance به کوئری
        $stmt = $conn->prepare("SELECT u.username, u.email, u.wallet_address, u.balance, u.win_balance, u.referral_balance,
                              IFNULL(gs.games_count, 0) as games_count,
                              IFNULL(gs.wins_count, 0) as wins_count,
                              IFNULL(gs.losses_count, 0) as losses_count
                              FROM users u
                              LEFT JOIN game_stats gs ON u.id = gs.user_id
                              WHERE u.id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // debug_log("User not found");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'User not found',
                'nonce' => $new_nonce
            ], 404);
        }

        $user = $result->fetch_assoc();

        // بررسی نوع خرید و موجودی مناسب
        if ($use_referral_balance) {
            // استفاده از موجودی رفرال
            $balance = (float)$user['referral_balance'];
            $balance_type = 'referral_balance';

            // debug_log("Using referral balance: $balance");

            if ($balance < $price) {
                // debug_log("Insufficient referral balance: $balance < $price");
                $conn->rollback();
                respondJSON([
                    'status' => 'error',
                    'message' => 'Not enough referral balance to purchase this car',
                    'nonce' => $new_nonce,
                    'referral_balance' => $balance,
                    'price' => $price
                ], 400);
            }
        } else {
            // استفاده از موجودی عادی
            $balance = (float)$user['balance'];
            $balance_type = 'balance';

            // debug_log("Using regular balance: $balance");

            if ($balance < $price) {
                // debug_log("Insufficient balance: $balance < $price");
                $conn->rollback();
                respondJSON([
                    'status' => 'error',
                    'message' => 'Not enough balance to purchase this car',
                    'nonce' => $new_nonce
                ], 400);
            }
        }

        // Update user balance - بروزرسانی فیلد مناسب بر اساس نوع خرید
        $new_balance = $balance - $price;

        // For free cars (price = 0), skip balance update since no change is needed
        if ($price > 0) {
            if ($use_referral_balance) {
                $stmt = $conn->prepare("UPDATE users SET referral_balance = ? WHERE id = ?");
            } else {
                $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
            }

            $stmt->bind_param('di', $new_balance, $user_id);
            $stmt->execute();

            if ($stmt->affected_rows === 0) {
                // debug_log("Failed to update user balance");
                $conn->rollback();
                respondJSON([
                    'status' => 'error',
                    'message' => 'Failed to update user balance',
                    'nonce' => $new_nonce
                ], 500);
            }

            // debug_log("User $balance_type updated to: $new_balance");
        } else {
            // debug_log("Free car - no balance update needed");
        }

        // Add car to user's cars - NOT activated by default (user needs to activate manually)
        $purchase_date = date('Y-m-d H:i:s');
        $is_activated = 0; // NOT activated by default - user needs to activate manually

        // debug_log("Preparing SQL: INSERT INTO user_cars (user_id, car_id, is_active, purchase_date) VALUES ($user_id, $car_id, $is_activated, $purchase_date)");

        $stmt = $conn->prepare("INSERT INTO user_cars (user_id, car_id, is_active, purchase_date) VALUES (?, ?, ?, ?)");
        if (!$stmt) {
            // debug_log("SQL Prepare Error: " . $conn->error);
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'Database error: ' . $conn->error,
                'nonce' => $new_nonce
            ], 500);
        }

        $stmt->bind_param('isis', $user_id, $car_id, $is_activated, $purchase_date);
        $result = $stmt->execute();

        if (!$result) {
            // debug_log("SQL Error: " . $conn->error . " - Statement error: " . $stmt->error);
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'Failed to add car to user: ' . $stmt->error,
                'nonce' => $new_nonce
            ], 500);
        }

        $car_id_in_db = $stmt->insert_id;
        // debug_log("Car added to user: ID=$car_id_in_db");

        // Commit transaction
        $conn->commit();

        // Get updated car list in both old and new format
        $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $cars_result = $stmt->get_result();

        // Old format (for backward compatibility)
        $cars_data = [];

        // New format - array of car objects
        $user_cars = [];

        while ($car = $cars_result->fetch_assoc()) {
            // Old format
            $cars_data[] = $car['car_id'] . ' ' . $car['is_activated'];

            // New format
            $user_cars[] = [
                'id' => (int)$car['id'],
                'user_id' => $car['user_id'],
                'car_id' => $car['car_id'],
                'is_activated' => (int)$car['is_activated'],
                'purchase_date' => $car['purchase_date']
            ];
        }

        // debug_log("Updated car list: " . json_encode($user_cars));

        // دریافت مقادیر به‌روز شده موجودی‌ها
        $stmt = $conn->prepare("SELECT balance, referral_balance, win_balance FROM users WHERE id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $balance_result = $stmt->get_result();
        $updated_balances = $balance_result->fetch_assoc();

        // Generate a unique encryption key for this session (like in fetch_user_data)
        $unique_seed = $user_id . time() . bin2hex(random_bytes(8));
        $encryption_key = hash('sha256', $unique_seed);
        $short_encryption_key = substr($encryption_key, 0, 32);

        // Return success message with updated balance and user data
        // Format response to match fetch_user_data format for consistency
        respondJSON([
            'status' => 'success',
            'message' => 'Car purchased successfully',
            'user' => [
                'username' => $user['username'],
                'email' => $user['email'],
                'wallet_address' => $user['wallet_address'],
                'balance' => (float)$updated_balances['balance'],
                'win_balance' => (float)$updated_balances['win_balance'],
                'referral_balance' => (float)$updated_balances['referral_balance'],
                'games_count' => (int)$user['games_count'],
                'wins_count' => (int)$user['wins_count'],
                'losses_count' => (int)$user['losses_count'],
                'cars_data' => $cars_data,
                'user_cars' => $user_cars
            ],
            'encryption_key' => $short_encryption_key,
            'nonce' => $new_nonce
        ]);

    } catch (Exception $e) {
        // debug_log("Exception in purchase_car: " . $e->getMessage());
        // Ensure transaction is rolled back
        $conn->rollback();
        respondJSON([
            'status' => 'error',
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $new_nonce
        ], 500);
    }
}

/**
 * Activate a purchased car
 */
function activate_car($conn, $user_id, $car_id, $new_nonce) {
    // debug_log("Processing car activation: User=$user_id, Car=$car_id");

    try {
        // Start transaction
        $conn->begin_transaction();

        // Check if the user owns the car
        $stmt = $conn->prepare("SELECT id, is_active FROM user_cars WHERE user_id = ? AND car_id = ?");
        $stmt->bind_param('is', $user_id, $car_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // debug_log("Car not owned by user");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'You do not own this car',
                'nonce' => $new_nonce
            ], 400);
        }

        $car_data = $result->fetch_assoc();

        // Check if car is already activated
        if ($car_data['is_active'] == 1) {
            // debug_log("Car already activated");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'This car is already activated',
                'nonce' => $new_nonce
            ], 400);
        }

        // Get current active car if any (we need to deactivate it first)
        $stmt = $conn->prepare("SELECT id FROM user_cars WHERE user_id = ? AND is_active = 1");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Deactivate currently active car
            $current_car = $result->fetch_assoc();
            $inactive = 0;

            $stmt = $conn->prepare("UPDATE user_cars SET is_active = ? WHERE id = ?");
            $stmt->bind_param('ii', $inactive, $current_car['id']);
            $stmt->execute();

            // debug_log("Deactivated previous active car: ID=" . $current_car['id']);
        }

        // Activate the requested car
        $active = 1;
        $stmt = $conn->prepare("UPDATE user_cars SET is_active = ? WHERE id = ?");
        $stmt->bind_param('ii', $active, $car_data['id']);
        $stmt->execute();

        if ($stmt->affected_rows === 0) {
            // debug_log("Failed to activate car");
            $conn->rollback();
            respondJSON([
                'status' => 'error',
                'message' => 'Failed to activate car',
                'nonce' => $new_nonce
            ], 500);
        }

        // debug_log("Car activated successfully: ID=" . $car_data['id']);

        // Commit transaction
        $conn->commit();

        // Get updated car list in both old and new format
        $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $cars_result = $stmt->get_result();

        // Get user data (including balance)
        $stmt = $conn->prepare("SELECT username, balance FROM users WHERE id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $user_result = $stmt->get_result();
        $user = $user_result->fetch_assoc();

        // Old format (for backward compatibility)
        $cars_data = [];

        // New format - array of car objects
        $user_cars = [];

        while ($car = $cars_result->fetch_assoc()) {
            // Old format
            $cars_data[] = $car['car_id'] . ' ' . $car['is_activated'];

            // New format
            $user_cars[] = [
                'id' => (int)$car['id'],
                'user_id' => $car['user_id'],
                'car_id' => $car['car_id'],
                'is_activated' => (int)$car['is_activated'],
                'purchase_date' => $car['purchase_date']
            ];
        }

        // debug_log("Updated car list: " . json_encode($user_cars));

        // Return success message with updated user data
        respondJSON([
            'status' => 'success',
            'message' => 'Car activated successfully',
            'balance' => (float)$user['balance'],
            'cars_data' => $cars_data,
            'user' => [
                'username' => $user['username'],
                'balance' => (float)$user['balance'],
                'cars_data' => $cars_data,
                'user_cars' => $user_cars
            ],
            'nonce' => $new_nonce
        ]);

    } catch (Exception $e) {
        // debug_log("Exception in activate_car: " . $e->getMessage());
        // Ensure transaction is rolled back
        $conn->rollback();
        respondJSON([
            'status' => 'error',
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $new_nonce
        ], 500);
    }
}

/**
 * Get car price based on car ID
 */
function get_car_price($car_id) {
    // Car price list (hardcoded for now, could be moved to database)
    $car_prices = [
        'Car:0' => 0,
        'Car:1' => 20,
        'Car:2' => 30,
        'Car:3' => 40,
        'Car:4' => 50,
        'Car:5' => 60,
        'Car:6' => 70,
        'Car:7' => 80,
        'Car:8' => 90,
        'Car:9' => 100
    ];

    // Check if car exists in price list
    if (!isset($car_prices[$car_id])) {
        return false;
    }

    return $car_prices[$car_id];
}

/**
 * Get all available cars and their prices
 */
function get_all_cars($nonce) {
    // debug_log("Getting all available cars and their prices");

    // Get car price list from the other function
    $cars = [];

    // Car price list (hardcoded for now, should match get_car_price function)
    $car_prices = [
        'Car:0' => 0,
        'Car:1' => 20,
        'Car:2' => 30,
        'Car:3' => 40,
        'Car:4' => 50,
        'Car:5' => 60,
        'Car:6' => 70,
        'Car:7' => 80,
        'Car:8' => 90,
        'Car:9' => 100
    ];

    // Car names (for better UX)
    $car_names = [
        'Car:0' => 'Basic Car',
        'Car:1' => 'Sport Car',
        'Car:2' => 'Truck',
        'Car:3' => 'SUV',
        'Car:4' => 'Luxury Car',
        'Car:5' => 'Electric Car',
        'Car:6' => 'Supercar',
        'Car:7' => 'Hypercar',
        'Car:8' => 'Vintage Car',
        'Car:9' => 'Concept Car'
    ];

    // Create array of car objects
    foreach ($car_prices as $car_id => $price) {
        $cars[] = [
            'id' => $car_id,
            'name' => isset($car_names[$car_id]) ? $car_names[$car_id] : $car_id,
            'price' => $price
        ];
    }

    // Return car list
    respondJSON([
        'status' => 'success',
        'cars' => $cars,
        'nonce' => $nonce
    ]);
}

// Note: Both respondJSON and checkRateLimit functions are imported from security.php
?>

