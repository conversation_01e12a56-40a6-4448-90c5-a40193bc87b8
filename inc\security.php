<?php
require_once 'db.php';

// Disable verbose error logging
ini_set('log_errors', 1);
ini_set('error_log', 'security_errors.log');

// Helper function for security logs - only log errors
function security_log($message) {
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false ||
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false ||
        strpos(strtolower($message), 'invalid') !== false) {
        error_log(date('Y-m-d H:i:s') . " - " . $message, 0);
    }
}

// Removed static JWT_SECRET
define('JWT_EXPIRY', 24 * 60 * 60); // 24 hours in seconds

/**
 * مکانیزم اول تولید کلید - بر اساس اطلاعات کاربر و داده‌های دیتابیس
 *
 * @param int $user_id شناسه کاربر
 * @return string|false کلید امنیتی یا false در صورت خطا
 */
function generatePrimaryUserSecret($user_id) {
    global $conn;

    try {
        // ترکیب اطلاعات منحصربفرد کاربر برای تولید کلید
        $stmt = $conn->prepare("SELECT username, email, created_at FROM users WHERE id = ?");
        if (!$stmt) return false;

        $stmt->bind_param("i", $user_id);
        if (!$stmt->execute()) return false;

        $result = $stmt->get_result();
        if ($result->num_rows === 0) return false;

        $user = $result->fetch_assoc();

        // ترکیب اطلاعات کاربر با یک نمک تصادفی
        $baseString = $user_id . $user['username'] . $user['email'] . $user['created_at'] . bin2hex(random_bytes(8));

        // تولید کلید با یک الگوریتم هش قوی
        return hash('sha256', $baseString);
    } catch (Exception $e) {
        // غیرفعال کردن ثبت خطا برای جلوگیری از رشد فایل log
        // error_log("Error in generatePrimaryUserSecret: " . $e->getMessage());
        return false;
    }
}

/**
 * مکانیزم دوم تولید کلید - به عنوان پشتیبان در صورت خطا در مکانیزم اول
 *
 * @param int $user_id شناسه کاربر
 * @return string کلید امنیتی
 */
function generateBackupUserSecret($user_id) {
    try {
        // استفاده از شناسه کاربر و زمان فعلی و یک نمک تصادفی
        $timestamp = time();
        $randomSalt = bin2hex(random_bytes(16));

        // ترکیب داده‌ها با یک الگوریتم هش قوی
        return hash('sha256', $user_id . $timestamp . $randomSalt . $_SERVER['SERVER_NAME']);
    } catch (Exception $e) {
        // حتی در صورت خطا، یک کلید منحصربفرد تولید می‌کنیم
        // error_log("Error in generateBackupUserSecret: " . $e->getMessage());
        return hash('sha256', $user_id . time() . uniqid('', true));
    }
}

/**
 * Get or create JWT secret for a user
 *
 * @param int $user_id User ID
 * @return string JWT secret key for this user
 */
function getUserJwtSecret($user_id) {
    global $conn;

    // Check if user already has a secret
    $stmt = $conn->prepare("SELECT jwt_secret FROM user_jwt_secrets WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['jwt_secret'];
    }

    // No secret found, create a new one using primary mechanism
    $newSecret = generatePrimaryUserSecret($user_id);

    // If primary mechanism fails, use backup mechanism
    if (!$newSecret) {
        // غیرفعال کردن ثبت خطا
        // error_log("Primary secret generation failed for user $user_id, using backup mechanism");
        $newSecret = generateBackupUserSecret($user_id);
    }

    // Store the new secret
    $stmt = $conn->prepare("INSERT INTO user_jwt_secrets (user_id, jwt_secret) VALUES (?, ?)");
    $stmt->bind_param("is", $user_id, $newSecret);
    $success = $stmt->execute();

    if (!$success) {
        // If DB storage fails, still return a valid secret but log the error
        // error_log("Failed to store JWT secret for user $user_id in database");
        // Generate a new backup secret since we couldn't store the first one
        return generateBackupUserSecret($user_id);
    }

    return $newSecret;
}

/**
 * Update JWT secret for a user
 *
 * @param int $user_id User ID
 * @return string The new JWT secret
 */
function refreshUserJwtSecret($userId) {
    global $conn;

    try {
        // Generate a new JWT secret
        $newSecret = bin2hex(random_bytes(32));

        // Update in database
        $stmt = $conn->prepare("INSERT INTO user_jwt_secrets (user_id, jwt_secret) VALUES (?, ?) ON DUPLICATE KEY UPDATE jwt_secret = ?");
        $stmt->bind_param("iss", $userId, $newSecret, $newSecret);
        $result = $stmt->execute();

        if (!$result) {
            // غیرفعال کردن ثبت خطا
            // error_log("Failed to refresh JWT secret: " . $stmt->error);
            return false;
        }

        return $newSecret;
    } catch (Exception $e) {
        // غیرفعال کردن ثبت خطا
        // error_log("Exception in refreshUserJwtSecret: " . $e->getMessage());
        return false;
    }
}

/**
 * بی‌اعتبار کردن تمام توکن‌های کاربر
 *
 * @param int $user_id شناسه کاربر
 * @return void
 */
function invalidateUserTokens($user_id) {
    global $conn;

    // تغییر زمان انقضای تمام توکن‌های کاربر به زمان فعلی
    $stmt = $conn->prepare("UPDATE user_tokens SET expires_at = NOW() WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();

    // غیرفعال کردن ثبت خطا
    // error_log("All tokens invalidated for user $user_id");
}

/**
 * Generate JWT token
 *
 * @param int $user_id User ID
 * @return string JWT token
 */
function generateJWT($user_id) {
    // Get user-specific JWT secret
    $secret = getUserJwtSecret($user_id);

    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];

    $payload = [
        'user_id' => $user_id,
        'exp' => time() + JWT_EXPIRY, // Expires in 24 hours (defined in JWT_EXPIRY)
        'iat' => time(),
        'jti' => bin2hex(random_bytes(8)), // JWT ID
        'token_type' => 'access_token',
        'version' => '1.0'
    ];

    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));

    $signature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, $secret, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
}

/**
 * Verify JWT token
 *
 * @param string $token JWT token to verify
 * @return int|false User ID if valid, false otherwise
 */
function verifyJWT($token) {
    global $conn;

    $parts = explode('.', $token);

    if (count($parts) !== 3) {
        return false;
    }

    list($base64Header, $base64Payload, $base64Signature) = $parts;

    $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)), true);

    if (!$payload || !isset($payload['user_id']) || !isset($payload['exp'])) {
        return false;
    }

    // Check if token is expired
    if ($payload['exp'] < time()) {
        return false;
    }

    // Get the user-specific JWT secret
    $user_id = $payload['user_id'];

    // Check if user exists and is active
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ? AND status = 'active'");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false;
    }

    // Try to get user's saved secret from database
    $secret = getUserJwtSecret($user_id);

    // Verify signature with user's secret
    $expectedSignature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, $secret, true);
    $actualSignature = base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Signature));

    if (hash_equals($expectedSignature, $actualSignature)) {
        return $user_id; // Return the user_id integer instead of the payload array
    }

    // If verification with saved secret fails, try with a regenerated secret using primary mechanism
    $regeneratedSecret = generatePrimaryUserSecret($user_id);
    if ($regeneratedSecret) {
        $expectedSignature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, $regeneratedSecret, true);
        if (hash_equals($expectedSignature, $actualSignature)) {
            // غیرفعال کردن ثبت خطا
            // error_log("JWT verified with regenerated primary secret for user $user_id");
            return $user_id; // Return the user_id integer instead of the payload array
        }
    }

    // Last resort - try with backup mechanism
    $backupSecret = generateBackupUserSecret($user_id);
    $expectedSignature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, $backupSecret, true);
    if (hash_equals($expectedSignature, $actualSignature)) {
        // غیرفعال کردن ثبت خطا
        // error_log("JWT verified with backup secret for user $user_id");
        return $user_id; // Return the user_id integer instead of the payload array
    }

    return false;
}

/**
 * Generate a random key
 *
 * @param int $length Length of the key
 * @return string Random key
 */
function generateRandomKey($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Generate a nonce and store it
 *
 * @param int $user_id User ID
 * @return string Nonce
 */
function generateNonce($user_id) {
    global $conn;

    $nonce = bin2hex(random_bytes(16)); // 32 character nonce

    // Delete old nonces to prevent buildup - مشابه cleanupOldNonces ولی فقط پاکسازی nonce‌های منقضی شده
    // Changed from 1 HOUR to 24 HOURS for 24-hour token system
    $stmt = $conn->prepare("DELETE FROM nonces WHERE user_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $expired = $stmt->affected_rows;

    // اگر تعداد nonce‌های فعال بیشتر از 10 باشد، قدیمی‌ترین‌ها را حذف کن
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM nonces WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];

    if ($count >= 2) {
        $stmt = $conn->prepare("DELETE FROM nonces WHERE user_id = ? ORDER BY created_at ASC LIMIT ?");
        $deleteCount = $count - 1; // نگه داشتن 5 nonce آخر
        $stmt->bind_param("ii", $user_id, $deleteCount);
        $stmt->execute();
        //error_log("Cleaned up excess nonces for user $user_id: Deleted: " . $stmt->affected_rows);
    }

    // Store new nonce
    $stmt = $conn->prepare("INSERT INTO nonces (nonce, user_id) VALUES (?, ?)");
    $stmt->bind_param("si", $nonce, $user_id);
    $stmt->execute();

    return $nonce;
}

/**
 * Verify and use a nonce
 *
 * @param int $user_id User ID
 * @param string $nonce Nonce to verify
 * @return bool True if valid, false otherwise
 */
function verifyAndUseNonce($user_id, $nonce) {
    global $conn;

    // With 24-hour token system, empty nonce is allowed
    if (empty($nonce)) {
        // Log that we're using 24-hour token system without nonce
        // error_log("Using 24-hour token system without nonce for user $user_id");
        return true;
    }

    $stmt = $conn->prepare("SELECT id FROM nonces WHERE user_id = ? AND nonce = ?");
    $stmt->bind_param("is", $user_id, $nonce);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // error_log("Invalid nonce for user $user_id: $nonce");
        return false;
    }

    // Delete the nonce to prevent reuse
    $stmt = $conn->prepare("DELETE FROM nonces WHERE user_id = ? AND nonce = ?");
    $stmt->bind_param("is", $user_id, $nonce);
    $stmt->execute();

    return true;
}

/**
 * Store a key for the user (Key1)
 *
 * @param int $user_id User ID
 * @param string $key Key to store
 * @return bool Success status
 */
function storeUserKey($user_id, $key) {
    global $conn;

    // پاکسازی کلیدهای منقضی شده
    $stmt = $conn->prepare("DELETE FROM user_key_parts WHERE user_id = ? AND expires_at < NOW()");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();

    // بررسی تعداد کلیدهای فعال
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_key_parts WHERE user_id = ? AND expires_at > NOW()");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];

    // اگر تعداد کلیدهای فعال بیشتر از 5 باشد، قدیمی‌ترین‌ها را حذف کن
    if ($count >= 2) {
        $stmt = $conn->prepare("DELETE FROM user_key_parts WHERE user_id = ? ORDER BY created_at ASC LIMIT ?");
        $deleteCount = $count - 1; // نگه داشتن 3 کلید آخر
        $stmt->bind_param("ii", $user_id, $deleteCount);
        $stmt->execute();
       // error_log("Cleaned up excess keys for user $user_id: Deleted: " . $stmt->affected_rows);
    }

    // Expires in 24 hours
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Store new key
    $stmt = $conn->prepare("INSERT INTO user_key_parts (user_id, key_part, expires_at) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $key, $expiry);
    return $stmt->execute();
}

/**
 * Verify a user key (Key1)
 *
 * @param int $user_id User ID
 * @param string $key Key to verify
 * @return bool True if valid, false otherwise
 */
function verifyUserKey($user_id, $key) {
    global $conn;

    $stmt = $conn->prepare("SELECT id FROM user_key_parts WHERE user_id = ? AND key_part = ? AND used = 0 AND expires_at > NOW()");
    $stmt->bind_param("is", $user_id, $key);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->num_rows > 0;
}

/**
 * Check rate limit for a user
 *
 * @param int $user_id User ID
 * @return bool True if within limit, false if exceeded
 */
function checkRateLimit($user_id) {
    global $conn;

    // اطمینان از وجود جدول rate_limits
    try {
        // Get current rate limit record
        $stmt = $conn->prepare("SELECT request_count, last_request_time FROM rate_limits WHERE user_id = ?");
        if (!$stmt) {
            error_log("Failed to prepare SELECT statement: " . $conn->error);
            return true; // اجازه ادامه درخواست در صورت خطا
        }

        $stmt->bind_param("i", $user_id);
        $execResult = $stmt->execute();

        if (!$execResult) {
            error_log("Failed to execute SELECT statement: " . $stmt->error);
            return true; // اجازه ادامه درخواست در صورت خطا
        }

        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $request_count = $row['request_count'];
            $last_request = strtotime($row['last_request_time']);
            $current_time = time();

            // اگر از آخرین درخواست بیش از 1 ساعت گذشته باشد، شمارنده را ریست کن
            if ($current_time - $last_request > 3600) {
                $request_count = 1;
                $stmt = $conn->prepare("UPDATE rate_limits SET request_count = 1, last_request_time = NOW() WHERE user_id = ?");
                if (!$stmt) {
                    error_log("Failed to prepare UPDATE statement (reset): " . $conn->error);
                    return true; // اجازه ادامه درخواست در صورت خطا
                }

                $stmt->bind_param("i", $user_id);
            } else {
                // افزایش شمارنده درخواست
                $request_count++;
                $stmt = $conn->prepare("UPDATE rate_limits SET request_count = ?, last_request_time = NOW() WHERE user_id = ?");
                if (!$stmt) {
                    error_log("Failed to prepare UPDATE statement (increment): " . $conn->error);
                    return true; // اجازه ادامه درخواست در صورت خطا
                }

                $stmt->bind_param("ii", $request_count, $user_id);
            }
        } else {
            // اگر رکوردی وجود نداشت، یک رکورد جدید ایجاد کن
            $request_count = 1;
            $stmt = $conn->prepare("INSERT INTO rate_limits (user_id, request_count, last_request_time) VALUES (?, 1, NOW())");
            if (!$stmt) {
                error_log("Failed to prepare INSERT statement: " . $conn->error);
                return true; // اجازه ادامه درخواست در صورت خطا
            }

            $stmt->bind_param("i", $user_id);
        }

        $execResult = $stmt->execute();

        if (!$execResult) {
            error_log("Failed to execute statement: " . $stmt->error);
            return true; // اجازه ادامه درخواست در صورت خطا
        }

        // Log the rate limit update for debugging
        if (function_exists('debug_log')) {
            debug_log("Rate limit for user $user_id: Request count = $request_count, Timestamp = " . date('Y-m-d H:i:s'));
        }

        // اگر تعداد درخواست‌ها از 100 بیشتر باشد، خطا برگردان
        if ($request_count > 100) {
            error_log("Rate limit exceeded for user $user_id: $request_count requests in the last hour");
            return false;
        }

        return true;
    } catch (Exception $e) {
        error_log("Exception in checkRateLimit: " . $e->getMessage());
        return true; // اجازه ادامه درخواست در صورت استثنا
    }
}

/**
 * Store user token in database
 *
 * @param int $user_id User ID
 * @param string $token JWT token
 * @return bool Success status
 */
function storeUserToken($user_id, $token) {
    global $conn;

    // پاکسازی توکن‌های منقضی شده
    $stmt = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ? AND expires_at < NOW()");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();

    // بررسی تعداد توکن‌های فعال
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_tokens WHERE user_id = ? AND expires_at > NOW()");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];

    // اگر تعداد توکن‌های فعال بیشتر از 5 باشد، قدیمی‌ترین‌ها را حذف کن
    if ($count >= 2) {
        $stmt = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ? ORDER BY last_activity ASC LIMIT ?");
        $deleteCount = $count - 1; // نگه داشتن 3 توکن آخر
        $stmt->bind_param("ii", $user_id, $deleteCount);
        $stmt->execute();
        //error_log("Cleaned up excess tokens for user $user_id: Deleted: " . $stmt->affected_rows);
    }

    // Expires in 24 hours
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Store new token
    $stmt = $conn->prepare("INSERT INTO user_tokens (user_id, token, expires_at, last_activity) VALUES (?, ?, ?, NOW())");
    $stmt->bind_param("iss", $user_id, $token, $expiry);
    return $stmt->execute();
}

/**
 * Store a server-only key (Key2) - this would be stored in a secure location
 *
 * This is just a placeholder. In production, Key2 would be stored more securely.
 */
function getMirrorServerKey() {
    // کلید ثابت حذف شده و تابع null بر می‌گرداند
    // این مکانیزم پشتیبان دیگر استفاده نمی‌شود
    error_log("getMirrorServerKey called - No fallback key available");
    return null;
}

/**
 * Get headers from the request
 *
 * @return array Headers
 */
function getRequestHeaders() {
    $headers = [];
    foreach ($_SERVER as $key => $value) {
        if (substr($key, 0, 5) === 'HTTP_') {
            $header = str_replace(' ', '-', ucwords(str_replace('_', ' ', strtolower(substr($key, 5)))));
            $headers[$header] = $value;
        }
    }
    return $headers;
}

/**
 * Extract JWT token from Authorization header
 *
 * @return string|null Token or null if not found
 */
function getBearerToken() {
    $headers = getRequestHeaders();

    if (isset($headers['Authorization'])) {
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }

    return null;
}

/**
 * Respond with JSON
 *
 * @param array $data Response data
 * @param int $status HTTP status code
 */
function respondJSON($data, $status = 200) {
    // جلوگیری از نمایش خطاهای PHP
    @ini_set('display_errors', 0);
    @error_reporting(0);

    header('Content-Type: application/json');
    http_response_code($status);
    echo json_encode($data);
    exit;
}

/**
 * Generate a user-specific key2 for Mirror server
 *
 * @param int $userId User ID
 * @param string $sessionId Session ID
 * @return string|false Generated key2 or false on failure
 */
function generateUserKey2($userId, $sessionId = null) {
    global $conn;

    // بررسی اگر کلید با این session_id از قبل وجود دارد
    if (!empty($sessionId)) {
        $stmt = $conn->prepare("SELECT key_value FROM user_key2
                               WHERE user_id = ? AND session_id = ?
                               AND is_active = 1 AND expires_at > NOW()");
        $stmt->bind_param("is", $userId, $sessionId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $existingKey = $row['key_value'];
            error_log("Using existing key2 for user $userId with session $sessionId");

            // آپدیت زمان انقضا برای تمدید کلید
            $expiresAt = date('Y-m-d H:i:s', strtotime('+3 hours'));
            $stmt = $conn->prepare("UPDATE user_key2 SET last_used = NOW(), expires_at = ?
                                   WHERE user_id = ? AND session_id = ? AND key_value = ?");
            $stmt->bind_param("siss", $expiresAt, $userId, $sessionId, $existingKey);
            $stmt->execute();

            return $existingKey;
        }
    }

    // اگر به اینجا رسیدیم، یعنی نیاز به ساخت کلید جدید داریم

    // تمیز کردن کلیدهای منقضی شده یا غیرفعال
    $stmt = $conn->prepare("DELETE FROM user_key2
                           WHERE (user_id = ? AND expires_at < NOW())
                           OR (user_id = ? AND is_active = 0)");
    $stmt->bind_param("ii", $userId, $userId);
    $stmt->execute();

    // مکانیزم تلاش مجدد - حداکثر 2 بار تلاش با فاصله 2 ثانیه
    $maxRetries = 2;
    $retryCount = 0;
    $success = false;
    $key = null;

    while (!$success && $retryCount < $maxRetries) {
        // کلید جدید تولید می‌کنیم
        $key = bin2hex(random_bytes(16));

        // تاریخ انقضا (3 ساعت)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+3 hours'));

        // ذخیره در دیتابیس
        $stmt = $conn->prepare("INSERT INTO user_key2 (user_id, key_value, session_id, expires_at, last_used)
                               VALUES (?, ?, ?, ?, NOW())");
        $stmt->bind_param("isss", $userId, $key, $sessionId, $expiresAt);

        $success = $stmt->execute();

        if ($success) {
            error_log("Generated new key2 for user $userId with session $sessionId");
            return $key;
        }

        // اگر شکست خورد، تلاش مجدد بعد از 2 ثانیه
        $retryCount++;
        if ($retryCount < $maxRetries) {
            error_log("Failed to generate key2 for user $userId, retrying ($retryCount/$maxRetries)...");
            sleep(2);
        }
    }

    // اگر به اینجا رسیدیم، یعنی تمام تلاش‌ها شکست خورده‌اند
    error_log("All attempts to generate key2 for user $userId failed");

    // ثبت وضعیت قطع اتصال در دیتابیس
    recordDisconnectRequest($userId, $sessionId);

    // در اینجا نمی‌توانیم مستقیماً Unity را فراخوانی کنیم
    // اما می‌توانیم اطلاعاتی را در دیتابیس ثبت کنیم که MirrorServerAuthenticator در درخواست بعدی بررسی کند
    return false;
}

/**
 * Record a disconnect request for a user
 *
 * @param int $userId User ID
 * @param string $sessionId Session ID
 * @return bool Success status
 */
function recordDisconnectRequest($userId, $sessionId = null) {
    global $conn;

    try {
        $stmt = $conn->prepare("INSERT INTO disconnect_requests (user_id, session_id) VALUES (?, ?)");
        $stmt->bind_param("is", $userId, $sessionId);
        $result = $stmt->execute();

        if (!$result) {
            error_log("Failed to record disconnect request: " . $stmt->error);
        }

        return $result;
    } catch (Exception $e) {
        error_log("Exception in recordDisconnectRequest: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if there's a pending disconnect request for a session
 *
 * @param int $userId User ID
 * @param string $sessionId Session ID
 * @return bool True if disconnect is requested
 */
function checkDisconnectRequest($userId, $sessionId) {
    global $conn;

    // بررسی وجود جدول
    $result = $conn->query("SHOW TABLES LIKE 'disconnect_requests'");
    if ($result->num_rows == 0) {
        return false;
    }

    // بررسی وجود درخواست قطع اتصال
    $stmt = $conn->prepare("SELECT id FROM disconnect_requests
                           WHERE user_id = ? AND session_id = ? AND processed = 0");
    $stmt->bind_param("is", $userId, $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();

    $hasRequest = $result->num_rows > 0;

    if ($hasRequest) {
        // علامت‌گذاری درخواست به عنوان پردازش شده
        $stmt = $conn->prepare("UPDATE disconnect_requests SET processed = 1
                               WHERE user_id = ? AND session_id = ? AND processed = 0");
        $stmt->bind_param("is", $userId, $sessionId);
        $stmt->execute();

        error_log("Disconnect request found and marked as processed for user $userId with session $sessionId");
    }

    return $hasRequest;
}

/**
 * Verify user key2
 *
 * @param string $key2 Key to verify
 * @return bool True if valid
 */
function verifyUserKey2($key2) {
    global $conn;

    // ابتدا کلیدهای منقضی شده را حذف می‌کنیم
    $stmt = $conn->prepare("DELETE FROM user_key2 WHERE expires_at < NOW()");
    $stmt->execute();

    // بررسی کلید در دیتابیس
    $stmt = $conn->prepare("SELECT user_id, session_id FROM user_key2
                           WHERE key_value = ?
                           AND is_active = 1
                           AND expires_at > NOW()");
    $stmt->bind_param("s", $key2);
    $stmt->execute();
    $result = $stmt->get_result();

    $valid = $result->num_rows > 0;

    if ($valid) {
        $data = $result->fetch_assoc();
        $userId = $data['user_id'];
        $sessionId = $data['session_id'];
        error_log("Verifying key2: Valid - User ID: $userId, Session ID: $sessionId, Key length: " . strlen($key2));

        // آپدیت آخرین زمان استفاده از کلید
        $stmt = $conn->prepare("UPDATE user_key2 SET last_used = NOW() WHERE key_value = ?");
        $stmt->bind_param("s", $key2);
        $stmt->execute();
    } else {
        error_log("Verifying key2: Invalid - Key length: " . strlen($key2));
    }

    return $valid;
}

/**
 * Remove user key2
 *
 * @param int $userId User ID
 * @return bool Success status
 */
function removeUserKey2($userId) {
    global $conn;

    $stmt = $conn->prepare("DELETE FROM user_key2 WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $result = $stmt->execute();

    error_log("Removed key2 for user $userId: " . ($result ? "Success" : "Failed"));

    return $result;
}

/**
 * Verify Mirror server key
 *
 * @param string $key2 Key to verify
 * @return bool True if valid
 */
function verifyMirrorServerKey($key2) {
    // تغییر تابع برای استفاده از سیستم جدید user_key2
    return verifyUserKey2($key2);
}

/**
 * Remove user key2 for a specific session
 *
 * @param int $userId User ID
 * @param string $sessionId Session ID
 * @return bool Success status
 */
function removeUserKey2WithSession($userId, $sessionId) {
    global $conn;

    $stmt = $conn->prepare("DELETE FROM user_key2 WHERE user_id = ? AND session_id = ?");
    $stmt->bind_param("is", $userId, $sessionId);
    $result = $stmt->execute();

    $affectedRows = $stmt->affected_rows;
    error_log("Removed key2 for user $userId with session $sessionId: " .
              ($result ? "Success" : "Failed") . ", Affected rows: $affectedRows");

    return $result && $affectedRows > 0;
}

/**
 * Clean up old tokens for a user
 *
 * @param int $userId User ID
 * @return bool Success status
 */
function cleanupOldTokens($userId) {
    global $conn;

    // پاک کردن توکن‌های منقضی شده
    $stmt = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ? AND expires_at < NOW()");
    $stmt->bind_param("i", $userId);
    $result1 = $stmt->execute();
    $expired = $stmt->affected_rows;

    // نگه داشتن فقط 3 توکن آخر برای هر کاربر
    $stmt = $conn->prepare("DELETE FROM user_tokens WHERE user_id = ? AND id NOT IN (
                           SELECT id FROM (
                               SELECT id FROM user_tokens WHERE user_id = ?
                               ORDER BY last_activity DESC LIMIT 3
                           ) as latest_tokens
                           )");
    $stmt->bind_param("ii", $userId, $userId);
    $result2 = $stmt->execute();
    $deleted = $stmt->affected_rows;

   // error_log("Cleaned up tokens for user $userId: Expired: $expired, Deleted old: $deleted");

    return $result1 && $result2;
}

/**
 * Clean up old keys (Key1) for a user
 *
 * @param int $userId User ID
 * @return bool Success status
 */
function cleanupOldKeys($userId) {
    global $conn;

    // پاک کردن کلیدهای منقضی شده
    $stmt = $conn->prepare("DELETE FROM user_key_parts WHERE user_id = ? AND expires_at < NOW()");
    $stmt->bind_param("i", $userId);
    $result1 = $stmt->execute();
    $expired = $stmt->affected_rows;

    // نگه داشتن فقط 3 کلید آخر برای هر کاربر
    $stmt = $conn->prepare("DELETE FROM user_key_parts WHERE user_id = ? AND id NOT IN (
                           SELECT id FROM (
                               SELECT id FROM user_key_parts WHERE user_id = ?
                               ORDER BY created_at DESC LIMIT 3
                           ) as latest_keys
                           )");
    $stmt->bind_param("ii", $userId, $userId);
    $result2 = $stmt->execute();
    $deleted = $stmt->affected_rows;

   // error_log("Cleaned up keys for user $userId: Expired: $expired, Deleted old: $deleted");

    return $result1 && $result2;
}

/**
 * Clean up old nonces for a user
 *
 * @param int $userId User ID
 * @return bool Success status
 */
function cleanupOldNonces($userId) {
    global $conn;

    // پاک کردن nonce های قدیمی (بیشتر از 1 ساعت)
    $stmt = $conn->prepare("DELETE FROM nonces WHERE user_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->bind_param("i", $userId);
    $result = $stmt->execute();
    $deleted = $stmt->affected_rows;

    // نگه داشتن فقط 5 nonce آخر
    $stmt = $conn->prepare("DELETE FROM nonces WHERE user_id = ? AND id NOT IN (
                           SELECT id FROM (
                               SELECT id FROM nonces WHERE user_id = ?
                               ORDER BY created_at DESC LIMIT 5
                           ) as latest_nonces
                           )");
    $stmt->bind_param("ii", $userId, $userId);
    $result2 = $stmt->execute();
    $deleted += $stmt->affected_rows;

   // error_log("Cleaned up nonces for user $userId: Deleted: $deleted");

    return $result && $result2;
}