<?php
/**
 * Verify Stake API
 * 
 * This API verifies that a user has properly placed a stake
 * and has a valid race token before entering the race scene
 */

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', 'verify_stake_errors.log');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Helper function to safely log debug info
function debug_log($message) {
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false || 
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false) {
        error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, 'verify_stake_debug.log');
    }
}

// Start debugging - disabled regular logging
// debug_log("API request received: " . json_encode($_POST));

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Check if getBearerToken function exists
if (!function_exists('getBearerToken')) {
    function getBearerToken() {
        $headers = null;
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }

        if (!empty($headers) && preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    debug_log("Created getBearerToken function");
}

// Get authorization token
$token = getBearerToken();
debug_log("Token received: " . ($token ? "Yes" : "No"));

if (!$token) {
    respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
}

// Verify token
$user_id = verifyJWT($token);
debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

if (!$user_id) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    debug_log("Extracted user_id from array: " . $user_id);
}

// Check rate limit
if (!checkRateLimit($user_id)) {
    respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

debug_log("Received input: " . json_encode($input));

// Validate required fields
if (!isset($input['key1']) || !isset($input['nonce']) || !isset($input['race_token'])) {
    respondJSON(['status' => 'error', 'message' => 'Key1, nonce, and race_token are required'], 400);
}

$key1 = $input['key1'];
$nonce = $input['nonce'];
$race_token = $input['race_token'];
$stake_amount = isset($input['stake_amount']) ? $input['stake_amount'] : 0;

debug_log("Race Token: $race_token, Stake Amount: $stake_amount");

// Verify Key1
if (!verifyUserKey($user_id, $key1)) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
}

// Verify Nonce
if (!verifyAndUseNonce($user_id, $nonce)) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or used nonce'], 401);
}

// Generate a new nonce for the next request
$new_nonce = generateNonce($user_id);
debug_log("New nonce generated: $new_nonce");

// Verify race token
verify_race_token($conn, $user_id, $race_token, $stake_amount, $new_nonce);

/**
 * Verify that the race token is valid and matches the stake amount
 */
function verify_race_token($conn, $user_id, $race_token, $stake_amount, $nonce) {
    debug_log("Verifying race token for user ID: $user_id, Race Token: $race_token, Stake Amount: $stake_amount");
    
    try {
        // Check if race_tokens table exists
        $result = $conn->query("SHOW TABLES LIKE 'race_tokens'");
        if ($result->num_rows === 0) {
            debug_log("Race tokens table does not exist!");
            respondJSON([
                'status' => 'error',
                'message' => 'Race token system not initialized',
                'nonce' => $nonce
            ], 500);
        }
        
        // Debug: Check race_tokens table content
        $debug_query = $conn->query("SELECT COUNT(*) as token_count FROM race_tokens");
        $debug_result = $debug_query->fetch_assoc();
        $token_count = $debug_result['token_count'];
        debug_log("Current race_tokens table has $token_count records");
        
        // First check if token exists at all
        $stmt = $conn->prepare("SELECT * FROM race_tokens WHERE token = ?");
        $stmt->bind_param("s", $race_token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            debug_log("Race token not found. Checking if table is empty or token is incorrect");
            
            // Check if there are any tokens at all for this user
            $check_stmt = $conn->prepare("SELECT COUNT(*) as user_token_count FROM race_tokens WHERE user_id = ?");
            $check_stmt->bind_param("i", $user_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $row = $check_result->fetch_assoc();
            $user_token_count = $row['user_token_count'];
            
            debug_log("User has $user_token_count tokens in the database");
            
            if ($user_token_count == 0) {
                // For backward compatibility or if the database is messed up, 
                // create a new race token record for this verification
                debug_log("No tokens found for user. Creating emergency token record");
                
                // Create a new race token record
                $stmt = $conn->prepare("INSERT INTO race_tokens (user_id, token, stake_amount, expires_at) VALUES (?, ?, ?, ?)");
                date_default_timezone_set('Asia/Tehran'); // Set to your server's timezone
                
                // Make sure MySQL timezone is also set correctly
                $conn->query("SET time_zone = '+03:30'"); // Tehran is UTC+3:30
                
                $current_time = date('Y-m-d H:i:s');
                $expiry = date('Y-m-d H:i:s', strtotime($current_time . ' +5 minutes'));
                debug_log("Emergency token creation - current time: $current_time, expiry: $expiry");
                $stmt->bind_param("isds", $user_id, $race_token, $stake_amount, $expiry);
                $result = $stmt->execute();
                
                if ($result) {
                    debug_log("Created emergency race token record successfully");
                    // Now respond with success
                    respondJSON([
                        'status' => 'success',
                        'message' => 'Race token verified successfully (emergency mode)',
                        'stake_amount' => (float)$stake_amount,
                        'nonce' => $nonce
                    ]);
                } else {
                    debug_log("Failed to create emergency race token: " . $stmt->error);
                    respondJSON([
                        'status' => 'error',
                        'message' => 'Failed to verify race token (emergency mode)',
                        'nonce' => $nonce
                    ], 500);
                }
                return;
            }
            
            respondJSON([
                'status' => 'error',
                'message' => 'Race token not found in the system',
                'nonce' => $nonce
            ], 401);
        }
        
        $token_data = $result->fetch_assoc();
        $token_user_id = $token_data['user_id'];
        $token_stake_amount = (float)$token_data['stake_amount'];
        $token_used = $token_data['used'];
        
        // Fix timezone issue - set timezone explicitly
        date_default_timezone_set('Asia/Tehran'); // Set to your server's timezone
        
        // Make sure MySQL timezone is also set correctly
        $conn->query("SET time_zone = '+03:30'"); // Tehran is UTC+3:30
        
        $current_time = date('Y-m-d H:i:s');
        $token_expired = strtotime($token_data['expires_at']) < strtotime($current_time);
        
        debug_log("Token details: user_id = $token_user_id, stake_amount = $token_stake_amount, used = $token_used");
        debug_log("Expiration check: token expires at " . $token_data['expires_at'] . ", current time is $current_time, expired = " . ($token_expired ? "Yes" : "No"));
        
        // Check if token belongs to this user
        if ($token_user_id != $user_id) {
            respondJSON([
                'status' => 'error',
                'message' => 'This race token belongs to another user',
                'nonce' => $nonce
            ], 401);
        }
        
        // Check if token is already used - but don't fail for now, just warn
        if ($token_used) {
            debug_log("Token is already marked as used - rejecting verification");
            respondJSON([
                'status' => 'error',
                'message' => 'Race token already used',
                'nonce' => $nonce
            ], 401);
        }
        
        // Check if token is expired - but don't fail for now, just warn
        if ($token_expired) {
            debug_log("Token is expired - rejecting verification");
            respondJSON([
                'status' => 'error',
                'message' => 'Race token has expired',
                'nonce' => $nonce
            ], 401);
        }
        
        // If stake amount is provided, verify it matches - warn but don't fail
        if ($stake_amount > 0 && $token_stake_amount != $stake_amount) {
            debug_log("Stake amount mismatch: token = $token_stake_amount, request = $stake_amount - rejecting verification");
            respondJSON([
                'status' => 'error',
                'message' => 'Stake amount mismatch',
                'nonce' => $nonce
            ], 400);
        }
        
        // Update token usage - mark token as used after successful verification
        $stmt = $conn->prepare("UPDATE race_tokens SET used = 1 WHERE user_id = ? AND token = ?");
        $stmt->bind_param("is", $user_id, $race_token);
        $stmt->execute();
        
        // Return success response
        respondJSON([
            'status' => 'success',
            'message' => 'Race token verified successfully',
            'stake_amount' => $token_stake_amount,
            'nonce' => $nonce
        ]);
        
    } catch (Exception $e) {
        debug_log("Exception in verify_race_token: " . $e->getMessage());
        respondJSON([
            'status' => 'error', 
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $nonce
        ], 500);
    }
}
?> 