using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using Mirror;
using System.Linq;
using UnityEngine.UI;
using UnityEngine.Networking;
using Newtonsoft.Json;
using System;

namespace Mirror.Examples.MultipleAdditiveScenes
{
    public struct RcStakMessage : NetworkMessage
    {
        public string stakValue;
    }

    [AddComponentMenu("")]
    public class RcMultiSceneNetManager : NetworkManager
    {
        [Header("MultiScene Setup")]
        [Tooltip("Maximum number of players per scene")]
        public int maxPlayersPerScene = 3;

        [Tooltip("Maximum number of scenes allowed")]
        public int maxScenes = 1000;

        [Scene]
        public string gameScene;

        [Header("Car Settings")]
        [Tooltip("Array of car prefabs that can be spawned as player prefabs")]
        public GameObject[] carPrefabs;

        [Header("Server Settings")]
        [Tooltip("URL to get the active car from the server")]
        public string getActiveCarUrl = "https://game-gofaster.com/gam/api/get_active_car.php";

        [Head<PERSON>("Fallback Settings")]
        [Tooltip("Index of the default car to spawn if server request fails")]
        public int defaultCarIndex = 0;

        bool subscenesLoaded;
        readonly List<Scene> subScenes = new List<Scene>();
        int clientIndex;

        private Dictionary<Scene, List<NetworkIdentity>> playersInScenes = new Dictionary<Scene, List<NetworkIdentity>>();
        private Dictionary<Scene, int> sceneCapacities = new Dictionary<Scene, int>();
        private Dictionary<string, Scene> stakToSceneMap = new Dictionary<string, Scene>();
        private Dictionary<NetworkConnectionToClient, string> connectionStaks = new Dictionary<NetworkConnectionToClient, string>();
        private Dictionary<NetworkConnectionToClient, int> connectionCarIndices = new Dictionary<NetworkConnectionToClient, int>();
        private HashSet<NetworkConnectionToClient> spawnedConnections = new HashSet<NetworkConnectionToClient>();

        public override void OnStartServer()
        {
            base.OnStartServer();
            playersInScenes.Clear();
            sceneCapacities.Clear();
            stakToSceneMap.Clear();
            connectionStaks.Clear();
            connectionCarIndices.Clear();
            spawnedConnections.Clear();

            // Debug car prefabs array
            Debug.Log($"[RcMultiSceneNetManager] 🚗 Car Prefabs Array Status:");
            Debug.Log($"[RcMultiSceneNetManager]   - Array is null: {carPrefabs == null}");
            Debug.Log($"[RcMultiSceneNetManager]   - Array length: {(carPrefabs != null ? carPrefabs.Length : 0)}");
            if (carPrefabs != null)
            {
                for (int i = 0; i < carPrefabs.Length; i++)
                {
                    Debug.Log($"[RcMultiSceneNetManager]   - Index {i}: {(carPrefabs[i] != null ? carPrefabs[i].name : "NULL")}");
                }
            }
            Debug.Log($"[RcMultiSceneNetManager]   - Default car index: {defaultCarIndex}");
            Debug.Log($"[RcMultiSceneNetManager]   - Get active car URL: {getActiveCarUrl}");

            // Register all car prefabs as spawnable objects
            RegisterCarPrefabs();

            NetworkServer.RegisterHandler<RcStakMessage>(OnReceiveStakMessage);
            NetworkServer.RegisterHandler<RcCarIndexMessage>(OnReceiveCarIndexMessage);
            StartCoroutine(ServerLoadInitialSubScenes());
        }

        public override void OnStartClient()
        {
            // Register car prefabs BEFORE calling base to ensure they're available
            RegisterCarPrefabs();

            base.OnStartClient();

            // Only get car data if we're a pure client (not host)
            if (!NetworkServer.active)
            {
                Debug.Log("[RcMultiSceneNetManager] 🔗 Pure client started - will get car data from server");
                StartCoroutine(GetCarDataAndSendToServer());
            }
            NetworkClient.RegisterHandler<RcStakMessage>(OnStakMessageReceived, false);
        }

        public override void OnClientConnect()
        {
            base.OnClientConnect();
            SendStakToServer();
        }

        private void OnReceiveStakMessage(NetworkConnectionToClient conn, RcStakMessage msg)
        {
            connectionStaks[conn] = msg.stakValue;
            Debug.Log($"Server received Stak '{msg.stakValue}' from client {conn.connectionId}");
        }

        private void OnReceiveCarIndexMessage(NetworkConnectionToClient conn, RcCarIndexMessage msg)
        {
            connectionCarIndices[conn] = msg.carIndex;
            Debug.Log($"[RcMultiSceneNetManager] 📥 Server received car index {msg.carIndex} from client {conn.connectionId}");
        }

        /// <summary>
        /// Message structure for sending car index from client to server
        /// </summary>
        public struct RcCarIndexMessage : NetworkMessage
        {
            public int carIndex;
        }

        /// <summary>
        /// Client-side method to get car data from server and send to MultiSceneNetManager
        /// </summary>
        private IEnumerator GetCarDataAndSendToServer()
        {
            Debug.Log("[RcMultiSceneNetManager] 🚀 Client getting car data from server...");

            int maxRetries = 3;
            float retryDelay = 2f;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                Debug.Log($"[RcMultiSceneNetManager] 🔄 Client attempt {attempt}/{maxRetries} to get car data");

                // Get authentication token
                string token = GetAuthenticationToken();
                if (string.IsNullOrEmpty(token))
                {
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: No authentication token available");
                    if (attempt < maxRetries)
                    {
                        Debug.Log($"[RcMultiSceneNetManager] ⏳ Waiting {retryDelay} seconds before retry...");
                        yield return new WaitForSeconds(retryDelay);
                        continue;
                    }
                    else
                    {
                        Debug.LogError("[RcMultiSceneNetManager] ❌ CRITICAL: Failed to get authentication token after all retries!");
                        yield break; // Don't send anything - let server disconnect us
                    }
                }

                // Get nonce for security
                string nonce = GetCurrentNonce();

                // Build the URL with nonce parameter
                string url = getActiveCarUrl;
                if (!string.IsNullOrEmpty(nonce))
                {
                    url += (url.Contains("?") ? "&" : "?") + "nonce=" + UnityWebRequest.EscapeURL(nonce);
                }

                Debug.Log($"[RcMultiSceneNetManager] 🌐 Client attempt {attempt}: Making HTTP request: {url}");

                // Create the web request
                UnityWebRequest request = UnityWebRequest.Get(url);
                request.SetRequestHeader("Authorization", "Bearer " + token);

                // Send the request
                yield return request.SendWebRequest();

                // Handle response
                if (request.result != UnityWebRequest.Result.Success)
                {
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: HTTP Error: {request.error} - Response Code: {request.responseCode}");
                    if (attempt < maxRetries)
                    {
                        Debug.Log($"[RcMultiSceneNetManager] ⏳ Waiting {retryDelay} seconds before retry...");
                        yield return new WaitForSeconds(retryDelay);
                        continue;
                    }
                    else
                    {
                        Debug.LogError("[RcMultiSceneNetManager] ❌ CRITICAL: HTTP request failed after all retries!");
                        yield break; // Don't send anything - let server disconnect us
                    }
                }

                // Process the response
                string responseText = request.downloadHandler.text;
                Debug.Log($"[RcMultiSceneNetManager] 📥 Client attempt {attempt}: Server response: {responseText}");

                try
                {
                    // Parse the response
                    var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseText);

                    if (response != null && response.ContainsKey("status") && response["status"].ToString() == "success")
                    {
                        // Get the active car ID
                        if (response.ContainsKey("active_car"))
                        {
                            string activeCarId = response["active_car"].ToString();
                            Debug.Log($"[RcMultiSceneNetManager] 🚗 Client attempt {attempt}: Active car ID from server: '{activeCarId}'");

                            int carIndex = GetCarIndexFromId(activeCarId);
                            if (carIndex >= 0)
                            {
                                SendCarIndexToServer(carIndex);
                                Debug.Log($"[RcMultiSceneNetManager] ✅ Client: Successfully sent car index {carIndex} to server");
                                yield break; // Success!
                            }
                            else
                            {
                                Debug.LogError($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: Invalid car index {carIndex} for car ID '{activeCarId}'");
                            }
                        }
                        else
                        {
                            Debug.LogWarning($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: No 'active_car' field found in response");
                        }
                    }
                    else
                    {
                        string errorMessage = response != null && response.ContainsKey("message") ?
                            response["message"].ToString() : "Unknown error";
                        Debug.LogError($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: Server error: {errorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Attempt {attempt}: Error parsing response: {ex.Message}");
                }

                // If we reach here, this attempt failed
                if (attempt < maxRetries)
                {
                    Debug.Log($"[RcMultiSceneNetManager] ⏳ Waiting {retryDelay} seconds before retry...");
                    yield return new WaitForSeconds(retryDelay);
                }
            }

            // If we reach here, all attempts failed
            Debug.LogError("[RcMultiSceneNetManager] ❌ CRITICAL: Failed to get car data after all retries!");
            Debug.LogError("[RcMultiSceneNetManager] ❌ Not sending any car index - server will disconnect us");
        }

        /// <summary>
        /// Send car index to server
        /// </summary>
        private void SendCarIndexToServer(int carIndex)
        {
            if (NetworkClient.isConnected)
            {
                Debug.Log($"[RcMultiSceneNetManager] 📤 Client sending car index {carIndex} to server");
                NetworkClient.Send(new RcCarIndexMessage { carIndex = carIndex });
            }
            else
            {
                Debug.LogError("[RcMultiSceneNetManager] ❌ Client not connected, cannot send car index");
            }
        }

        /// <summary>
        /// Register all car prefabs as spawnable objects in NetworkManager
        /// </summary>
        private void RegisterCarPrefabs()
        {
            if (carPrefabs == null || carPrefabs.Length == 0)
            {
                Debug.LogWarning("[RcMultiSceneNetManager] ⚠️ No car prefabs to register");
                return;
            }

            string side = NetworkServer.active ? "Server" : "Client";
            Debug.Log($"[RcMultiSceneNetManager] 📝 {side}: Registering {carPrefabs.Length} car prefabs as spawnable objects...");

            for (int i = 0; i < carPrefabs.Length; i++)
            {
                if (carPrefabs[i] != null)
                {
                    // Check if prefab has NetworkIdentity
                    NetworkIdentity netId = carPrefabs[i].GetComponent<NetworkIdentity>();
                    if (netId != null)
                    {
                        // Add to spawnPrefabs list if not already there
                        if (!spawnPrefabs.Contains(carPrefabs[i]))
                        {
                            spawnPrefabs.Add(carPrefabs[i]);
                            Debug.Log($"[RcMultiSceneNetManager] ✅ {side}: Registered car prefab {i}: {carPrefabs[i].name} (AssetId: {netId.assetId})");
                        }
                        else
                        {
                            Debug.Log($"[RcMultiSceneNetManager] ℹ️ {side}: Car prefab {i}: {carPrefabs[i].name} already registered (AssetId: {netId.assetId})");
                        }

                        // Also register with NetworkClient if we're on client side
                        if (!NetworkServer.active)
                        {
                            try
                            {
                                NetworkClient.RegisterPrefab(carPrefabs[i]);
                                Debug.Log($"[RcMultiSceneNetManager] 🔗 Client: Also registered with NetworkClient: {carPrefabs[i].name} (AssetId: {netId.assetId})");
                            }
                            catch (System.Exception ex)
                            {
                                Debug.LogError($"[RcMultiSceneNetManager] ❌ Failed to register prefab with NetworkClient: {carPrefabs[i].name} - {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        Debug.LogError($"[RcMultiSceneNetManager] ❌ {side}: Car prefab {i}: {carPrefabs[i].name} has no NetworkIdentity component!");
                    }
                }
                else
                {
                    Debug.LogWarning($"[RcMultiSceneNetManager] ⚠️ {side}: Car prefab {i} is NULL");
                }
            }

            Debug.Log($"[RcMultiSceneNetManager] 📝 {side}: Total spawnable prefabs after registration: {spawnPrefabs.Count}");
        }

        private void OnStakMessageReceived(RcStakMessage msg)
        {
            // فعلاً نیازی به دریافت پیام از سرور نداریم
        }

        private void SendStakToServer()
        {
            if (NetworkClient.isConnected)
            {
                string stakValue = GetSecureStakeAmount();

                RcStakMessage msg = new RcStakMessage { stakValue = stakValue };
                NetworkClient.Send(msg);
                Debug.Log($"[RcMultiSceneNetManager] 🔒 Client sent secure Stak: '{stakValue}' to server");
            }
        }

        /// <summary>
        /// Get stake amount securely with multiple security layers
        /// Priority: 1) RaceManager verified stake, 2) Encrypted PlayerPrefs, 3) Regular PlayerPrefs fallback
        /// </summary>
        private string GetSecureStakeAmount()
        {
            try
            {
                // Layer 1: Get verified stake from RaceManager (Most Secure)
                float verifiedStake = GetVerifiedStakeFromRaceManager();
                if (verifiedStake > 0)
                {
                    Debug.Log($"[RcMultiSceneNetManager] 🔒 Security Layer 1: Using verified stake from RaceManager: {verifiedStake}");
                    return verifiedStake.ToString("F2");
                }

                // Layer 2: Get from encrypted PlayerPrefs (Secure)
                string encryptedStake = GetDecryptedStakeFromPlayerPrefs();
                if (!string.IsNullOrEmpty(encryptedStake))
                {
                    Debug.LogWarning($"[RcMultiSceneNetManager] ⚠️ Security Layer 2: Using encrypted PlayerPrefs stake: {encryptedStake}");
                    return encryptedStake;
                }

                // Layer 3: Fallback to regular PlayerPrefs (Least Secure)
                float fallbackStake = PlayerPrefs.GetFloat("SelectedStakeAmount", 0);
                if (fallbackStake > 0)
                {
                    Debug.LogWarning($"[RcMultiSceneNetManager] ⚠️ Security Layer 3: Using fallback PlayerPrefs stake: {fallbackStake} (LEAST SECURE)");
                    return fallbackStake.ToString("F2");
                }

                // No valid stake found
                Debug.LogError("[RcMultiSceneNetManager] ❌ No valid stake amount found in any security layer!");
                return "0.00";
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Error in GetSecureStakeAmount: {ex.Message}");
                return "0.00";
            }
        }

        /// <summary>
        /// Get verified stake amount from RaceManager (Most Secure)
        /// </summary>
        private float GetVerifiedStakeFromRaceManager()
        {
            try
            {
                // Use reflection to access RaceManager from different namespace
                var raceManagerType = System.Type.GetType("RaceManager");
                if (raceManagerType != null)
                {
                    var instanceProperty = raceManagerType.GetProperty("Instance", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (instanceProperty != null)
                    {
                        var instance = instanceProperty.GetValue(null);
                        if (instance != null)
                        {
                            // Check if stake is verified
                            var isStakeVerifiedProperty = raceManagerType.GetProperty("IsStakeVerified");
                            var verifiedStakeAmountProperty = raceManagerType.GetProperty("VerifiedStakeAmount");

                            if (isStakeVerifiedProperty != null && verifiedStakeAmountProperty != null)
                            {
                                bool isVerified = (bool)isStakeVerifiedProperty.GetValue(instance);
                                if (isVerified)
                                {
                                    float verifiedAmount = (float)verifiedStakeAmountProperty.GetValue(instance);
                                    Debug.Log($"[RcMultiSceneNetManager] ✅ Retrieved verified stake from RaceManager: {verifiedAmount}");
                                    return verifiedAmount;
                                }
                                else
                                {
                                    Debug.LogWarning("[RcMultiSceneNetManager] ⚠️ RaceManager stake not yet verified by server");
                                }
                            }
                        }
                    }
                }
                else
                {
                    Debug.LogWarning("[RcMultiSceneNetManager] ⚠️ RaceManager type not found");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Error accessing RaceManager via reflection: {ex.Message}");
            }

            return 0f;
        }

        /// <summary>
        /// Get stake amount from encrypted PlayerPrefs (Secure fallback)
        /// </summary>
        private string GetDecryptedStakeFromPlayerPrefs()
        {
            try
            {
                string encryptedStakeAmount = PlayerPrefs.GetString("EncryptedStakeAmount", "");
                if (!string.IsNullOrEmpty(encryptedStakeAmount))
                {
                    // Get encryption key from SessionManager
                    string encryptionKey = GetEncryptionKey();
                    if (!string.IsNullOrEmpty(encryptionKey))
                    {
                        string decryptedStake = DecryptStakeAmount(encryptedStakeAmount, encryptionKey);
                        if (!string.IsNullOrEmpty(decryptedStake) && float.TryParse(decryptedStake, out float amount) && amount > 0)
                        {
                            Debug.Log($"[RcMultiSceneNetManager] 🔓 Successfully decrypted stake amount: {amount}");
                            return amount.ToString("F2");
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Error decrypting stake from PlayerPrefs: {ex.Message}");
            }

            return "";
        }

        /// <summary>
        /// Get encryption key from SessionManager
        /// </summary>
        private string GetEncryptionKey()
        {
            try
            {
                var sessionManagerType = System.Type.GetType("SessionManager");
                if (sessionManagerType != null)
                {
                    var instanceProperty = sessionManagerType.GetProperty("Instance", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (instanceProperty != null)
                    {
                        var instance = instanceProperty.GetValue(null);
                        if (instance != null)
                        {
                            var key1Property = sessionManagerType.GetProperty("Key1");
                            if (key1Property != null)
                            {
                                string key = (string)key1Property.GetValue(instance);
                                return key;
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Error getting encryption key: {ex.Message}");
            }

            return "";
        }

        /// <summary>
        /// Decrypt stake amount using AES encryption
        /// </summary>
        private string DecryptStakeAmount(string encryptedData, string key)
        {
            try
            {
                // This is a simplified decryption - in real implementation you'd use proper AES
                // For now, we'll use a basic XOR decryption as placeholder
                byte[] data = System.Convert.FromBase64String(encryptedData);
                byte[] keyBytes = System.Text.Encoding.UTF8.GetBytes(key);

                for (int i = 0; i < data.Length; i++)
                {
                    data[i] = (byte)(data[i] ^ keyBytes[i % keyBytes.Length]);
                }

                return System.Text.Encoding.UTF8.GetString(data);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Decryption error: {ex.Message}");
                return "";
            }
        }

        private Scene? FindSceneForStak(string stak)
        {
            if (stakToSceneMap.ContainsKey(stak))
            {
                Scene scene = stakToSceneMap[stak];
                if (playersInScenes.ContainsKey(scene) && playersInScenes[scene].Count < maxPlayersPerScene && sceneCapacities[scene] < maxPlayersPerScene)
                {
                    Debug.Log($"Found existing scene for Stak '{stak}' with {playersInScenes[scene].Count} players.");
                    return scene;
                }
                Debug.Log($"Scene for Stak '{stak}' is full, will create a new one.");
                return null;
            }

            foreach (Scene scene in subScenes)
            {
                if (!playersInScenes.ContainsKey(scene) || playersInScenes[scene].Count == 0)
                {
                    if (!playersInScenes.ContainsKey(scene))
                    {
                        playersInScenes[scene] = new List<NetworkIdentity>();
                    }
                    sceneCapacities[scene] = 0;
                    stakToSceneMap[stak] = scene;
                    Debug.Log($"Assigned empty scene to Stak '{stak}'.");
                    return scene;
                }
            }

            Debug.Log($"No empty scene available for Stak '{stak}', creating new one.");
            return null;
        }

        IEnumerator CreateNewSubScene(string stak = "")
        {
            if (subScenes.Count >= maxScenes)
            {
                Debug.LogWarning("Reached the maximum number of scenes allowed!");
                yield break;
            }

            yield return SceneManager.LoadSceneAsync(gameScene, new LoadSceneParameters { loadSceneMode = LoadSceneMode.Additive, localPhysicsMode = LocalPhysicsMode.Physics3D });

            Scene newScene = SceneManager.GetSceneAt(SceneManager.sceneCount - 1);
            subScenes.Add(newScene);
            sceneCapacities[newScene] = 0;
            playersInScenes[newScene] = new List<NetworkIdentity>();

            if (!string.IsNullOrEmpty(stak))
            {
                stakToSceneMap[stak] = newScene;
                Debug.Log($"Created new scene for Stak '{stak}'.");
            }
        }

        IEnumerator EnsureEmptyScene()
        {
            if (subScenes.Count == 0 || subScenes.All(scene => playersInScenes.ContainsKey(scene) && playersInScenes[scene].Count > 0))
            {
                yield return CreateNewSubScene();
            }
        }

        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            StartCoroutine(OnServerAddPlayerDelayed(conn));
        }

        /// <summary>
        /// Get the active car from the server for a specific connection
        /// Only works in host mode - dedicated server should never call this
        /// </summary>
        private IEnumerator GetActiveCarFromServer(NetworkConnectionToClient conn)
        {
            // This should only be called in host mode
            if (NetworkServer.active && !NetworkClient.active)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ CRITICAL: GetActiveCarFromServer called on dedicated server for connection {conn.connectionId}!");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ This should never happen - disconnecting client");
                conn.Disconnect();
                yield break;
            }

            Debug.Log($"[RcMultiSceneNetManager] 🔐 Getting authentication data for connection {conn.connectionId}");

            // Get authentication token using reflection to avoid namespace issues
            string token = GetAuthenticationToken();
            Debug.Log($"[RcMultiSceneNetManager] 🎫 Token retrieved: {(!string.IsNullOrEmpty(token) ? "✅ SUCCESS" : "❌ FAILED")}");
            if (!string.IsNullOrEmpty(token))
            {
                string tokenPreview = token.Length > 20 ? token.Substring(0, 10) + "..." + token.Substring(token.Length - 10) : token;
                Debug.Log($"[RcMultiSceneNetManager] 🎫 Token preview: {tokenPreview}");
            }

            if (string.IsNullOrEmpty(token))
            {
                Debug.LogError("[RcMultiSceneNetManager] ❌ No authentication token available - disconnecting client");
                conn.Disconnect();
                yield break;
            }

            // Get nonce for security using reflection
            string nonce = GetCurrentNonce();
            Debug.Log($"[RcMultiSceneNetManager] 🔒 Nonce retrieved: {(!string.IsNullOrEmpty(nonce) ? "✅ SUCCESS" : "⚠️ EMPTY")}");

            // Build the URL with nonce parameter
            string url = getActiveCarUrl;
            if (!string.IsNullOrEmpty(nonce))
            {
                url += (url.Contains("?") ? "&" : "?") + "nonce=" + UnityWebRequest.EscapeURL(nonce);
            }

            Debug.Log($"[RcMultiSceneNetManager] 🌐 Making HTTP request for connection {conn.connectionId}:");
            Debug.Log($"[RcMultiSceneNetManager]   - URL: {url}");
            Debug.Log($"[RcMultiSceneNetManager]   - Has nonce: {!string.IsNullOrEmpty(nonce)}");

            // Create the web request
            UnityWebRequest request = UnityWebRequest.Get(url);
            request.SetRequestHeader("Authorization", "Bearer " + token);
            Debug.Log($"[RcMultiSceneNetManager] 📤 Sending HTTP request...");

            // Send the request
            yield return request.SendWebRequest();

            Debug.Log($"[RcMultiSceneNetManager] 📥 HTTP request completed:");
            Debug.Log($"[RcMultiSceneNetManager]   - Response Code: {request.responseCode}");
            Debug.Log($"[RcMultiSceneNetManager]   - Result: {request.result}");
            Debug.Log($"[RcMultiSceneNetManager]   - Error: {request.error}");
            Debug.Log($"[RcMultiSceneNetManager]   - Raw Response: {request.downloadHandler.text}");

            // Handle response
            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ HTTP Error for connection {conn.connectionId}: {request.error}");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Response Code: {request.responseCode}");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client - cannot proceed without car data");
                conn.Disconnect();
                yield break;
            }

            // Process the response
            string responseText = request.downloadHandler.text;
            Debug.Log($"[RcMultiSceneNetManager] 📥 Active car response for connection {conn.connectionId}: {responseText}");

            try
            {
                // Parse the response
                var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseText);

                if (response != null && response.ContainsKey("status") && response["status"].ToString() == "success")
                {
                    Debug.Log($"[RcMultiSceneNetManager] ✅ Server response status: success");

                    // Get the active car ID
                    if (response.ContainsKey("active_car"))
                    {
                        string activeCarId = response["active_car"].ToString();
                        Debug.Log($"[RcMultiSceneNetManager] 🚗 Active car ID from server for connection {conn.connectionId}: '{activeCarId}'");

                        // Also log car_data if available
                        if (response.ContainsKey("car_data"))
                        {
                            Debug.Log($"[RcMultiSceneNetManager] 📊 Car data: {response["car_data"]}");
                        }

                        int carIndex = GetCarIndexFromId(activeCarId);
                        connectionCarIndices[conn] = carIndex;

                        Debug.Log($"[RcMultiSceneNetManager] ✅ Set car index {carIndex} for connection {conn.connectionId}");
                        Debug.Log($"[RcMultiSceneNetManager] 📊 connectionCarIndices now contains {connectionCarIndices.Count} entries:");
                        foreach (var kvp in connectionCarIndices)
                        {
                            Debug.Log($"[RcMultiSceneNetManager]   - Connection {kvp.Key.connectionId}: Car Index {kvp.Value}");
                        }
                    }
                    else
                    {
                        Debug.LogError($"[RcMultiSceneNetManager] ❌ No 'active_car' field found in response for connection {conn.connectionId}");
                        Debug.LogError($"[RcMultiSceneNetManager] Available response keys: {string.Join(", ", response.Keys)}");
                        Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client - invalid server response");
                        conn.Disconnect();
                        yield break;
                    }
                }
                else
                {
                    string status = response != null && response.ContainsKey("status") ? response["status"].ToString() : "unknown";
                    string errorMessage = response != null && response.ContainsKey("message") ?
                        response["message"].ToString() : "Unknown error";
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Server response status: {status}, error: {errorMessage}");
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client - server error");
                    conn.Disconnect();
                    yield break;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Error parsing active car response for connection {conn.connectionId}: {ex.Message}");
                Debug.LogError($"[RcMultiSceneNetManager] Raw response: {responseText}");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client - parsing error");
                conn.Disconnect();
                yield break;
            }
        }

        /// <summary>
        /// Convert car ID to index in the carPrefabs array
        /// </summary>
        private int GetCarIndexFromId(string carId)
        {
            Debug.Log($"[RcMultiSceneNetManager] Converting car ID to index: '{carId}'");
            Debug.Log($"[RcMultiSceneNetManager] Available car prefabs count: {(carPrefabs != null ? carPrefabs.Length : 0)}");

            if (string.IsNullOrEmpty(carId))
            {
                Debug.LogError("[RcMultiSceneNetManager] ❌ Car ID is null or empty!");
                return -1; // Return invalid index instead of default
            }

            // This implementation depends on your car ID format
            // Example: "Car:1" -> 1, "Car:0" -> 0
            if (carId.StartsWith("Car:"))
            {
                string indexStr = carId.Substring(4);
                Debug.Log($"[RcMultiSceneNetManager] Extracted index string: '{indexStr}'");

                if (int.TryParse(indexStr, out int index))
                {
                    Debug.Log($"[RcMultiSceneNetManager] Parsed index: {index}");

                    if (carPrefabs != null && index >= 0 && index < carPrefabs.Length)
                    {
                        if (carPrefabs[index] != null)
                        {
                            Debug.Log($"[RcMultiSceneNetManager] ✅ Valid car index: {index} (Car prefab: {carPrefabs[index].name})");
                            return index;
                        }
                        else
                        {
                            Debug.LogError($"[RcMultiSceneNetManager] ❌ Car prefab at index {index} is NULL!");
                            return -1;
                        }
                    }
                    else
                    {
                        Debug.LogError($"[RcMultiSceneNetManager] ❌ Car index out of range: {index}, valid range is 0-{(carPrefabs != null ? carPrefabs.Length - 1 : -1)}!");
                        return -1;
                    }
                }
                else
                {
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Failed to parse car index from: '{indexStr}'!");
                    return -1;
                }
            }
            else
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Car ID does not start with 'Car:': '{carId}'!");
                return -1;
            }
        }

        /// <summary>
        /// Get authentication token using reflection to avoid namespace issues
        /// </summary>
        private string GetAuthenticationToken()
        {
            try
            {
                // Try to find SessionManager using reflection
                var sessionManagerType = System.Type.GetType("SessionManager");
                if (sessionManagerType != null)
                {
                    var instanceProperty = sessionManagerType.GetProperty("Instance", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (instanceProperty != null)
                    {
                        var instance = instanceProperty.GetValue(null);
                        if (instance != null)
                        {
                            var isLoggedInProperty = sessionManagerType.GetProperty("IsLoggedIn");
                            var jwtTokenProperty = sessionManagerType.GetProperty("JWT_Token");

                            if (isLoggedInProperty != null && jwtTokenProperty != null)
                            {
                                bool isLoggedIn = (bool)isLoggedInProperty.GetValue(instance);
                                if (isLoggedIn)
                                {
                                    string token = (string)jwtTokenProperty.GetValue(instance);
                                    if (!string.IsNullOrEmpty(token))
                                    {
                                        Debug.Log("Token retrieved from SessionManager via reflection");
                                        return token;
                                    }
                                }
                            }
                        }
                    }
                }

                // Fallback to Login static property using reflection
                var loginType = System.Type.GetType("Login");
                if (loginType != null)
                {
                    var jwtTokenProperty = loginType.GetProperty("JWT_Token", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (jwtTokenProperty != null)
                    {
                        string token = (string)jwtTokenProperty.GetValue(null);
                        if (!string.IsNullOrEmpty(token))
                        {
                            Debug.Log("Token retrieved from Login static property via reflection");
                            return token;
                        }
                    }
                }

                // Final fallback to PlayerPrefs
                string playerPrefsToken = PlayerPrefs.GetString("JWT_Token", "");
                if (!string.IsNullOrEmpty(playerPrefsToken))
                {
                    Debug.Log("Token retrieved from PlayerPrefs as fallback");
                    return playerPrefsToken;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error getting authentication token via reflection: {ex.Message}");
            }

            Debug.LogError("No authentication token found");
            return "";
        }

        /// <summary>
        /// Get current nonce using reflection to avoid namespace issues
        /// </summary>
        private string GetCurrentNonce()
        {
            try
            {
                // Try to find SessionManager using reflection
                var sessionManagerType = System.Type.GetType("SessionManager");
                if (sessionManagerType != null)
                {
                    var instanceProperty = sessionManagerType.GetProperty("Instance", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (instanceProperty != null)
                    {
                        var instance = instanceProperty.GetValue(null);
                        if (instance != null)
                        {
                            var currentNonceProperty = sessionManagerType.GetProperty("CurrentNonce");
                            if (currentNonceProperty != null)
                            {
                                string nonce = (string)currentNonceProperty.GetValue(instance);
                                if (!string.IsNullOrEmpty(nonce))
                                {
                                    Debug.Log("Nonce retrieved from SessionManager via reflection");
                                    return nonce;
                                }
                            }
                        }
                    }
                }

                // Fallback to Login static property using reflection
                var loginType = System.Type.GetType("Login");
                if (loginType != null)
                {
                    var currentNonceProperty = loginType.GetProperty("CurrentNonce", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (currentNonceProperty != null)
                    {
                        string nonce = (string)currentNonceProperty.GetValue(null);
                        if (!string.IsNullOrEmpty(nonce))
                        {
                            Debug.Log("Nonce retrieved from Login static property via reflection");
                            return nonce;
                        }
                    }
                }

                // Final fallback to PlayerPrefs
                string playerPrefsNonce = PlayerPrefs.GetString("Nonce", "");
                if (!string.IsNullOrEmpty(playerPrefsNonce))
                {
                    Debug.Log("Nonce retrieved from PlayerPrefs as fallback");
                    return playerPrefsNonce;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error getting nonce via reflection: {ex.Message}");
            }

            Debug.Log("No nonce found, continuing without nonce");
            return "";
        }

        IEnumerator OnServerAddPlayerDelayed(NetworkConnectionToClient conn)
        {
            // Check if player already spawned for this connection
            if (spawnedConnections.Contains(conn))
            {
                Debug.LogWarning($"[RcMultiSceneNetManager] ⚠️ Player already spawned for connection {conn.connectionId} - ignoring duplicate spawn request");
                yield break;
            }

            // Mark this connection as being processed
            spawnedConnections.Add(conn);
            Debug.Log($"[RcMultiSceneNetManager] 🔒 Marked connection {conn.connectionId} as spawning - total spawned: {spawnedConnections.Count}");

            while (!subscenesLoaded)
                yield return null;

            float timeout = 5f;
            float elapsed = 0f;
            while (!connectionStaks.ContainsKey(conn) && elapsed < timeout)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }

            if (!connectionStaks.ContainsKey(conn))
            {
                connectionStaks[conn] = "DefaultStak";
                Debug.LogWarning($"No Stak received from client {conn.connectionId} within {timeout} seconds, using 'DefaultStak'");
            }

            string stak = connectionStaks[conn];
            Debug.Log($"Player connecting with Stak: '{stak}'");

            // Wait for car index from client or get from server (for host mode)
            if (NetworkServer.active && NetworkClient.active)
            {
                // Host mode - get car data from server
                Debug.Log($"[RcMultiSceneNetManager] 🏠 Host mode - getting active car from server for connection {conn.connectionId}");
                yield return StartCoroutine(GetActiveCarFromServer(conn));
            }
            else
            {
                // Dedicated server mode - wait for car index from client
                Debug.Log($"[RcMultiSceneNetManager] 🖥️ Dedicated server - waiting for car index from client {conn.connectionId}");
                float carTimeout = 30f; // Increased timeout
                float carElapsed = 0f;
                while (!connectionCarIndices.ContainsKey(conn) && carElapsed < carTimeout)
                {
                    carElapsed += Time.deltaTime;
                    yield return null;
                }

                if (!connectionCarIndices.ContainsKey(conn))
                {
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ CRITICAL: No car index received from client {conn.connectionId} within {carTimeout} seconds!");
                    Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client {conn.connectionId} - cannot spawn without proper car data");
                    spawnedConnections.Remove(conn); // Remove from spawned list before disconnect
                    conn.Disconnect();
                    yield break;
                }
                else
                {
                    Debug.Log($"[RcMultiSceneNetManager] ✅ Received car index {connectionCarIndices[conn]} from client {conn.connectionId}");
                }
            }

            Scene? targetScene = FindSceneForStak(stak);

            if (!targetScene.HasValue)
            {
                yield return CreateNewSubScene(stak);
                targetScene = FindSceneForStak(stak);
            }

            if (!targetScene.HasValue)
            {
                Debug.LogWarning($"No available space in any scene for new player with Stak: '{stak}'");
                spawnedConnections.Remove(conn);
                conn.Disconnect();
                yield break;
            }

            conn.Send(new SceneMessage { sceneName = gameScene, sceneOperation = SceneOperation.LoadAdditive });

            yield return new WaitForEndOfFrame();

            // Get the car index for this connection - MUST exist (no fallback)
            if (!connectionCarIndices.ContainsKey(conn))
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ CRITICAL: No car index found for connection {conn.connectionId}!");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ This should never happen - disconnecting client");
                spawnedConnections.Remove(conn); // Remove from spawned list before disconnect
                conn.Disconnect();
                yield break;
            }

            int carIndex = connectionCarIndices[conn];
            Debug.Log($"[RcMultiSceneNetManager] 🔍 Retrieved car index for connection {conn.connectionId}: {carIndex}");

            // Validate car index and get prefab
            GameObject selectedCarPrefab = null;
            if (carPrefabs != null && carIndex >= 0 && carIndex < carPrefabs.Length && carPrefabs[carIndex] != null)
            {
                selectedCarPrefab = carPrefabs[carIndex];
                Debug.Log($"[RcMultiSceneNetManager] 🚗 Selected car prefab for connection {conn.connectionId}:");
                Debug.Log($"[RcMultiSceneNetManager]   - Car Index: {carIndex}");
                Debug.Log($"[RcMultiSceneNetManager]   - Selected Prefab: {selectedCarPrefab.name}");
                Debug.Log($"[RcMultiSceneNetManager]   - Total Car Prefabs: {carPrefabs.Length}");
            }
            else
            {
                Debug.LogError($"[RcMultiSceneNetManager] ❌ CRITICAL: Invalid car index {carIndex} or NULL prefab!");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Car prefabs array length: {(carPrefabs != null ? carPrefabs.Length : 0)}");
                Debug.LogError($"[RcMultiSceneNetManager] ❌ Disconnecting client {conn.connectionId}");
                spawnedConnections.Remove(conn); // Remove from spawned list before disconnect
                conn.Disconnect();
                yield break;
            }

            // Spawn the selected car prefab with proper positioning
            // Get spawn position and rotation using NetworkManager's method
            Transform startPos = GetStartPosition();
            Vector3 spawnPosition = startPos != null ? startPos.position : Vector3.zero;
            Quaternion spawnRotation = startPos != null ? startPos.rotation : Quaternion.identity;

            Debug.Log($"[RcMultiSceneNetManager] 🎯 Spawn position for {selectedCarPrefab.name}: {spawnPosition}");

            // Instantiate the selected car at the proper position
            GameObject player = Instantiate(selectedCarPrefab, spawnPosition, spawnRotation);

            // Add player for connection
            NetworkServer.AddPlayerForConnection(conn, player);

            Debug.Log($"[RcMultiSceneNetManager] ✅ Spawned {selectedCarPrefab.name} for connection {conn.connectionId} at position {spawnPosition}");
            Debug.Log($"[RcMultiSceneNetManager] 📊 Total spawned connections: {spawnedConnections.Count}");

            // PlayerScore functionality removed to avoid cross-assembly SyncVar modification errors
            int sceneIndex = subScenes.IndexOf(targetScene.Value);

            if (subScenes.Count > 0)
            {
                SceneManager.MoveGameObjectToScene(conn.identity.gameObject, targetScene.Value);

                if (!playersInScenes.ContainsKey(targetScene.Value))
                {
                    playersInScenes[targetScene.Value] = new List<NetworkIdentity>();
                }
                playersInScenes[targetScene.Value].Add(conn.identity);

                if (sceneCapacities[targetScene.Value] < maxPlayersPerScene)
                {
                    sceneCapacities[targetScene.Value]++;
                }

                Debug.Log($"Player added to scene {targetScene.Value.name} with Stak '{stak}'. Total players: {playersInScenes[targetScene.Value].Count}");
            }

            clientIndex++;

            StartCoroutine(EnsureEmptyScene());
        }

        public override void OnServerDisconnect(NetworkConnectionToClient conn)
        {
            if (conn.identity != null)
            {
                Scene? sceneToRemove = null;

                foreach (var kvp in playersInScenes)
                {
                    if (kvp.Value.Contains(conn.identity))
                    {
                        kvp.Value.Remove(conn.identity);
                        string stak = connectionStaks.ContainsKey(conn) ? connectionStaks[conn] : "UnknownStak";
                        Debug.Log($"Player with Stak '{stak}' disconnected from scene {kvp.Key.name}. Remaining players: {kvp.Value.Count}");
                        if (kvp.Value.Count == 0)
                        {
                            sceneToRemove = kvp.Key;
                        }
                        break;
                    }
                }

                if (sceneToRemove.HasValue)
                {
                    StartCoroutine(RemoveEmptyScene(sceneToRemove.Value));
                }
            }

            if (connectionStaks.ContainsKey(conn))
            {
                connectionStaks.Remove(conn);
            }

            if (connectionCarIndices.ContainsKey(conn))
            {
                connectionCarIndices.Remove(conn);
            }

            if (spawnedConnections.Contains(conn))
            {
                spawnedConnections.Remove(conn);
                Debug.Log($"[RcMultiSceneNetManager] 🧹 Cleaned up spawned connection {conn.connectionId} - remaining spawned: {spawnedConnections.Count}");
            }

            base.OnServerDisconnect(conn);
        }

        private IEnumerator RemoveEmptyScene(Scene scene)
        {
            if (scene.IsValid())
            {
                yield return SceneManager.UnloadSceneAsync(scene);
            }

            string stakToRemove = null;
            foreach (var kvp in stakToSceneMap)
            {
                if (kvp.Value == scene)
                {
                    stakToRemove = kvp.Key;
                    break;
                }
            }
            if (stakToRemove != null)
            {
                stakToSceneMap.Remove(stakToRemove);
            }

            subScenes.Remove(scene);
            playersInScenes.Remove(scene);
            sceneCapacities.Remove(scene);

            Debug.Log($"Scene {scene.name} was empty and has been removed.");
        }

        #region Start & Stop Callbacks

        IEnumerator ServerLoadInitialSubScenes()
        {
            for (int index = 0; index < 2; index++)
            {
                yield return CreateNewSubScene();
            }

            subscenesLoaded = true;

            StartCoroutine(EnsureEmptyScene());
        }

        public override void OnStopServer()
        {
            NetworkServer.SendToAll(new SceneMessage { sceneName = gameScene, sceneOperation = SceneOperation.UnloadAdditive });
            StartCoroutine(ServerUnloadSubScenes());
            clientIndex = 0;
            playersInScenes.Clear();
            sceneCapacities.Clear();
            stakToSceneMap.Clear();
            connectionStaks.Clear();
            connectionCarIndices.Clear();
            spawnedConnections.Clear();
        }

        IEnumerator ServerUnloadSubScenes()
        {
            for (int index = 0; index < subScenes.Count; index++)
                if (subScenes[index].IsValid())
                    yield return SceneManager.UnloadSceneAsync(subScenes[index]);

            subScenes.Clear();
            subscenesLoaded = false;

            yield return Resources.UnloadUnusedAssets();
        }

        public override void OnStopClient()
        {
            if (mode == NetworkManagerMode.Offline)
                StartCoroutine(ClientUnloadSubScenes());
        }

        IEnumerator ClientUnloadSubScenes()
        {
            for (int index = 0; index < SceneManager.sceneCount; index++)
                if (SceneManager.GetSceneAt(index) != SceneManager.GetActiveScene())
                    yield return SceneManager.UnloadSceneAsync(SceneManager.GetSceneAt(index));
        }

        #endregion
    }
}
