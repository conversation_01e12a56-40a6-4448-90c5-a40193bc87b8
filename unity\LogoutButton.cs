using UnityEngine;
using UnityEngine.UI;
using Mirror;
using UnityEngine.SceneManagement;

/// <summary>
/// Handles logout functionality for both client and server
/// </summary>
public class LogoutButton : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private Button logoutButton;
    [SerializeField] private string loginSceneName = "SampleScene";
    
    [Header("Client Settings")]
    [SerializeField] private bool resetPlayerPrefs = true;
    [SerializeField] private bool deleteLocalFiles = false;
    
    void Start()
    {
        if (logoutButton != null)
        {
            logoutButton.onClick.AddListener(OnLogoutButtonClicked);
        }
    }
    
    /// <summary>
    /// Called when the logout button is clicked
    /// </summary>
    public void OnLogoutButtonClicked()
    {
        // پاک کردن داده‌های اصلی
        if (SessionManager.Instance != null)
            SessionManager.Instance.Logout();

        // پاک کردن داده‌های استاتیک
        Login.JWT_Token = null;
        Login.Key1 = null;
        Login.CurrentNonce = null;

        // پاک کردن PlayerPrefs
        PlayerPrefs.DeleteAll();
        PlayerPrefs.Save();

        // قطع اتصال شبکه اگر متصل است
        if (NetworkClient.active)
            NetworkManager.singleton.StopClient();
        if (NetworkServer.active)
            NetworkManager.singleton.StopHost();

        // بازگشت به صفحه لاگین
        SceneManager.LoadScene(loginSceneName);
    }
} 