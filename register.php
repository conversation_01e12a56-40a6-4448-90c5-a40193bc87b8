<?php
// Error handler at the very beginning of the file
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno]: $errstr in $errfile on line $errline");
    // For Unity compatibility, always return JSO<PERSON>
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'An internal error occurred. Please try again later.',
        'error_code' => 'SERVER_ERROR'
    ]);
    exit(0); // Exit with success status for Unity
});

// Exception handler
set_exception_handler(function($exception) {
    error_log("Uncaught Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    // For Unity compatibility, always return JSON
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'An internal error occurred. Please try again later.',
        'error_code' => 'SERVER_EXCEPTION'
    ]);
    exit(0); // Exit with success status for Unity
});

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Fatal Error: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);
        // Headers may already be sent in case of fatal error, but try anyway
        if (!headers_sent()) {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'A fatal error occurred. Please try again later.',
                'error_code' => 'FATAL_ERROR'
            ]);
        }
    }
});

// Basic validation check for db.php and security.php - avoid include failures
if (!file_exists('inc/db.php') || !file_exists('inc/security.php')) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Configuration error. Please contact support.',
        'error_code' => 'CONFIG_ERROR'
    ]);
    exit(0);
}

// Includes inside try-catch
try {
    require_once 'inc/db.php';
    require_once 'inc/security.php';
} catch (Exception $e) {
    error_log("Include error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'System configuration error. Please contact support.',
        'error_code' => 'INCLUDE_ERROR'
    ]);
    exit(0);
}

// Set timezone and other settings
date_default_timezone_set('Asia/Tehran');
ini_set('display_errors', 0);
ini_set('log_errors', 0);
ini_set('error_log', 'register_errors.log');

// Safe response function - always returns HTTP 200
function respondWithJson($data) {
    header('Content-Type: application/json');
    // Add timestamp to responses
    $data['timestamp'] = date('Y-m-d H:i:s');
    echo json_encode($data);
    exit(0); // Always exit with success status for Unity
}

// Check if register_attempts table exists
function ensureRegisterAttemptsTable($conn) {
    try {
        $result = $conn->query("SHOW TABLES LIKE 'register_attempts'");
        if ($result->num_rows == 0) {
            $conn->query("CREATE TABLE IF NOT EXISTS `register_attempts` (
                `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                `ip_address` varchar(45) NOT NULL,
                `attempt_count` int(11) NOT NULL DEFAULT 1,
                `last_attempt` timestamp NOT NULL DEFAULT current_timestamp(),
                UNIQUE KEY `ip_address` (`ip_address`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        }
        return true;
    } catch (Exception $e) {
        error_log("Could not ensure register_attempts table: " . $e->getMessage());
        return false;
    }
}

// Function to increment register attempt counter - simplified
function incrementRegisterAttempt($conn, $ip) {
    try {
        // First, ensure the table exists
        if (!ensureRegisterAttemptsTable($conn)) {
            return false;
        }
        
        // Try to update existing record first
        $stmt = $conn->prepare("UPDATE register_attempts SET attempt_count = attempt_count + 1, last_attempt = NOW() WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        
        // If no rows affected, insert new record
        if ($stmt->affected_rows == 0) {
            $stmt = $conn->prepare("INSERT INTO register_attempts (ip_address, attempt_count) VALUES (?, 1)");
            $stmt->bind_param("s", $ip);
            $stmt->execute();
        }
        
        // Get the current count for logging purposes
        $stmt = $conn->prepare("SELECT attempt_count FROM register_attempts WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $attempt_count = $row['attempt_count'];
            
            // Log if attempt count is high
            if ($attempt_count >= 3) {
                error_log("Multiple failed register attempts from IP: $ip (Attempt #$attempt_count)");
            }
            
            return $attempt_count;
        } else {
            return 1; // Fallback value
        }
    } catch (Exception $e) {
        error_log("Error incrementing register attempt: " . $e->getMessage());
        return false;
    }
}

// Function to check register rate limit - simplified
function checkRegisterRateLimit($conn, $ip) {
    try {
        // First, ensure the table exists
        if (!ensureRegisterAttemptsTable($conn)) {
            return true; // Allow register if we can't check
        }
        
        // Reset counters older than 15 minutes
        $conn->query("UPDATE register_attempts SET attempt_count = 1 WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
        
        // Get current count
        $stmt = $conn->prepare("SELECT attempt_count FROM register_attempts WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $attempt_count = $row['attempt_count'];
            
            // Maximum 5 attempts per 15 minutes
            if ($attempt_count >= 5) {
                error_log("Register rate limit exceeded for IP: $ip ($attempt_count attempts)");
                return false;
            }
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Error checking register rate limit: " . $e->getMessage());
        return true; // Allow register if we can't check
    }
}

// Test connection immediately
if (!isset($conn) || $conn->connect_error) {
    error_log("Database connection failed: " . ($conn->connect_error ?? "Unknown error"));
    respondWithJson([
        'status' => 'error',
        'message' => 'Database connection error. Please try again later.',
        'error_code' => 'DB_CONNECT_ERROR'
    ]);
}

// == MAIN REGISTER LOGIC ==
// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    respondWithJson([
        'status' => 'error',
        'message' => 'Method not allowed',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
}

// Get IP for rate limiting
$ip_address = $_SERVER['REMOTE_ADDR'];

// Check rate limiting
if (!checkRegisterRateLimit($conn, $ip_address)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Too many registration attempts. Please try again later.',
        'error_code' => 'RATE_LIMIT_EXCEEDED'
    ]);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

// Validate required fields
if (!isset($input['username']) || !isset($input['password']) || !isset($input['email'])) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Username, password and email are required',
        'error_code' => 'MISSING_FIELDS'
    ]);
}

$username = trim($input['username']);
$password = trim($input['password']);
$email = trim($input['email']);
$wallet_address = isset($input['wallet_address']) ? trim($input['wallet_address']) : null;
$referral_code = isset($input['referral_code']) ? trim($input['referral_code']) : null;

// Validate input
if (empty($username) || empty($password) || empty($email)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Username, password and email cannot be empty',
        'error_code' => 'EMPTY_FIELDS'
    ]);
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Invalid email format',
        'error_code' => 'INVALID_EMAIL'
    ]);
}

// Validate username format (alphanumeric, 3-20 characters)
if (!preg_match('/^[a-zA-Z0-9]{3,20}$/', $username)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Username must be 3-20 characters long and contain only letters and numbers',
        'error_code' => 'INVALID_USERNAME'
    ]);
}

// Validate password strength (minimum 8 characters)
if (strlen($password) < 8) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Password must be at least 8 characters long',
        'error_code' => 'WEAK_PASSWORD'
    ]);
}

// Validate wallet address if provided
if ($wallet_address && !preg_match('/^0x[a-fA-F0-9]{40}$/', $wallet_address)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Invalid wallet address format',
        'error_code' => 'INVALID_WALLET'
    ]);
}

// Increment register attempt before checking
incrementRegisterAttempt($conn, $ip_address);

// Check if username already exists
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        respondWithJson([
            'status' => 'error',
            'message' => 'Registration service error. Please try again later.',
            'error_code' => 'DB_PREPARE_ERROR'
        ]);
    }
    
    $stmt->bind_param("s", $username);
    $success = $stmt->execute();
    
    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        respondWithJson([
            'status' => 'error',
            'message' => 'Registration service error. Please try again later.',
            'error_code' => 'DB_EXECUTE_ERROR'
        ]);
    }
    
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        respondWithJson([
            'status' => 'error',
            'message' => 'Username already exists',
            'error_code' => 'USERNAME_EXISTS'
        ]);
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        respondWithJson([
            'status' => 'error',
            'message' => 'Email already exists',
            'error_code' => 'EMAIL_EXISTS'
        ]);
    }
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // تولید کد رفرال برای کاربر جدید
    $user_referral_code = '';
    $max_attempts = 10; // محدودیت تلاش برای تولید کد منحصر به فرد
    
    for ($i = 0; $i < $max_attempts; $i++) {
        $generated_code = generateReferralCode();
        
        // بررسی منحصر به فرد بودن کد
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ?");
        $check_stmt->bind_param("s", $generated_code);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows === 0) {
            $user_referral_code = $generated_code;
            break;
        }
    }
    
    if (empty($user_referral_code)) {
        $user_referral_code = generateReferralCode(10); // اگر نتوانستیم کد منحصر به فرد پیدا کنیم، طول را افزایش می‌دهیم
    }
    
    // Insert new user with referral code
    $stmt = $conn->prepare("INSERT INTO users (username, password, email, wallet_address, referral_code, status, created_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
    $stmt->bind_param("sssss", $username, $hashed_password, $email, $wallet_address, $user_referral_code);
    $success = $stmt->execute();
    
    if (!$success) {
        error_log("Insert failed: " . $stmt->error);
        respondWithJson([
            'status' => 'error',
            'message' => 'Registration failed. Please try again later.',
            'error_code' => 'DB_INSERT_ERROR'
        ]);
    }
    
    $user_id = $stmt->insert_id;
    
    // بررسی کد رفرال وارد شده - اگر معتبر باشد، پاداش به دعوت کننده داده می‌شود
    if (!empty($referral_code)) {
        $referrer_id = checkReferralCode($conn, $referral_code);
        
        if ($referrer_id && $referrer_id != $user_id) { // اطمینان از اینکه کاربر خودش را رفر نکرده
            // اضافه کردن رابطه به جدول referrals
            $referral_stmt = $conn->prepare("INSERT INTO referrals (referrer_id, referred_id, registration_rewarded, race_rewarded, created_at) VALUES (?, ?, 1, 0, NOW())");
            $referral_stmt->bind_param("ii", $referrer_id, $user_id);
            
            if ($referral_stmt->execute()) {
                // افزایش موجودی رفرال کاربر دعوت‌کننده
                $registration_reward = 0.5; // پاداش ثبت‌نام
                addReferralBalance($conn, $referrer_id, $registration_reward);
                
                error_log("Referral reward added: User $referrer_id received $registration_reward for referring user $user_id");
            }
        }
    }
    
    // Generate initial tokens and keys
    try {
        // Generate JWT secret
        $jwt_secret = refreshUserJwtSecret($user_id);
        
        // Generate initial token
        $token = generateJWT($user_id);
        storeUserToken($user_id, $token);
        
        // Generate initial key
        $key1 = generateRandomKey();
        storeUserKey($user_id, $key1);
        
        // Generate initial nonce
        $nonce = generateNonce($user_id);
        
        // Return success response
        respondWithJson([
            'status' => 'success',
            'message' => 'Registration successful',
            'token' => $token,
            'key1' => $key1,
            'nonce' => $nonce
        ]);
    } catch (Exception $e) {
        error_log("Token generation error: " . $e->getMessage());
        respondWithJson([
            'status' => 'error',
            'message' => 'Registration successful but failed to generate session tokens. Please try logging in.',
            'error_code' => 'TOKEN_GENERATION_ERROR'
        ]);
    }
} catch (Exception $e) {
    error_log("Register error: " . $e->getMessage());
    respondWithJson([
        'status' => 'error',
        'message' => 'Registration service error. Please try again later.',
        'error_code' => 'REGISTER_PROCESS_ERROR'
    ]);
}

// بعد از تأیید اعتبار کاربر، یک کد رفرال منحصر به فرد برای آن تولید می‌کنیم
// این تابع را در انتهای فایل تعریف می‌کنیم
function generateReferralCode($length = 8) {
    // فقط از حروف بزرگ و اعداد استفاده می‌کنیم تا خوانایی بهتر باشد
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[random_int(0, strlen($characters) - 1)];
    }
    return $code;
}

// تابع بررسی وجود کد رفرال
function checkReferralCode($conn, $code) {
    try {
        $stmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ?");
        $stmt->bind_param("s", $code);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return $row['id'];
        }
        return false;
    } catch (Exception $e) {
        error_log("Error checking referral code: " . $e->getMessage());
        return false;
    }
}

// تابع افزایش موجودی رفرال
function addReferralBalance($conn, $user_id, $amount) {
    try {
        $stmt = $conn->prepare("UPDATE users SET referral_balance = referral_balance + ? WHERE id = ?");
        $stmt->bind_param("di", $amount, $user_id);
        $stmt->execute();
        return $stmt->affected_rows > 0;
    } catch (Exception $e) {
        error_log("Error adding referral balance: " . $e->getMessage());
        return false;
    }
} 