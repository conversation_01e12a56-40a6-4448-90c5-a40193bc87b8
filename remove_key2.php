<?php
// Set timezone for consistent date/time operations
date_default_timezone_set('Asia/Tehran');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Log file for debugging
$log_file = 'logs/remove_key2.log';

// Enable error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', $log_file);

function debug_log($message) {
    global $log_file;
    
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false || 
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false ||
        strpos(strtolower($message), 'warning') !== false) {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    }
}

// Disabled regular logging
// debug_log("API Request received: remove_key2.php");

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

debug_log("Input data: " . json_encode($input));

// Validate required fields
if (!isset($input['token'])) {
    debug_log("Missing required fields");
    respondJSON(['status' => 'error', 'message' => 'Token is required'], 400);
}

$token = $input['token'];
$sessionId = isset($input['session_id']) ? $input['session_id'] : null;

debug_log("Received request with session ID: " . ($sessionId ? $sessionId : "None"));

// اضافه کردن بررسی session_id
if (empty($sessionId)) {
    debug_log("WARNING: Session ID is empty or not provided - Using user ID only for key removal");
}

// Verify token
$user_id = verifyJWT($token);
if (!$user_id) {
    debug_log("Invalid or expired token");
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    error_log("Extracted user_id from array: " . $user_id);
}

// Remove key2 for the user - اگر session_id ارسال شده باشد، فقط کلید مربوط به آن session را حذف می‌کنیم
$result = false;
if (!empty($sessionId)) {
    debug_log("Removing key2 for user: $user_id with session ID: $sessionId");
    $result = removeUserKey2WithSession($user_id, $sessionId);
} else {
    debug_log("Removing all keys for user: $user_id");
    $result = removeUserKey2($user_id);
}

if ($result) {
    debug_log("Key2 removed successfully for user: $user_id");
    respondJSON(['status' => 'success', 'message' => 'Key2 removed successfully']);
} else {
    debug_log("Failed to remove Key2 for user: $user_id");
    respondJSON(['status' => 'error', 'message' => 'Failed to remove Key2'], 500);
} 