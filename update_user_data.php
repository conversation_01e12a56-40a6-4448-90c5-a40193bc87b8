<?php
// Set timezone for consistent date/time operations
date_default_timezone_set('Asia/Tehran');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Log file for debugging
$log_file = 'logs/update_user_data.log';

// Enable error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', $log_file);

function debug_log($message) {
    global $log_file;
    
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false || 
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false ||
        strpos(strtolower($message), 'warning') !== false) {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    }
}

// Disabled regular logging
// debug_log("API Request received: update_user_data.php");

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

debug_log("Input data: " . json_encode($input));

// Validate required fields
if (!isset($input['token']) || !isset($input['key1']) || !isset($input['key2']) || !isset($input['nonce'])) {
    debug_log("Missing required fields");
    respondJSON(['status' => 'error', 'message' => 'Missing required fields'], 400);
}

$token = $input['token'];
$key1 = $input['key1'];
$key2 = $input['key2'];
$nonce = $input['nonce'];
$sessionId = isset($input['session_id']) ? $input['session_id'] : null;
$data = isset($input['data']) ? json_decode($input['data'], true) : null;

debug_log("Processing request with nonce: $nonce");
debug_log("Session ID received: " . ($sessionId ? $sessionId : "Not provided"));

// اضافه کردن بررسی session_id
if (empty($sessionId)) {
    debug_log("WARNING: Session ID is empty or not provided - This may cause issues with multi-player connections");
}

// Verify token
$user_id = verifyJWT($token);
if (!$user_id) {
    debug_log("Invalid or expired token");
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    error_log("Extracted user_id from array: " . $user_id);
}

// Check rate limit
if (!checkRateLimit($user_id)) {
    debug_log("Rate limit exceeded for user: $user_id");
    respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
}

// Verify Key1
debug_log("Verifying Key1");
if (!verifyUserKey($user_id, $key1)) {
    debug_log("Invalid or expired Key1");
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
}

// Verify Key2 (Mirror server key)
debug_log("Verifying Key2: [Length: " . strlen($key2) . "]");
// Log first and last few characters for debugging (don't log the full key for security)
if (strlen($key2) > 8) {
    $key2Start = substr($key2, 0, 4);
    $key2End = substr($key2, -4);
    debug_log("Key2 pattern: " . $key2Start . "..." . $key2End);
}

// Try to identify if key2 contains any problematic characters
$hasSpecialChars = preg_match('/[^a-zA-Z0-9]/', $key2);
debug_log("Key2 contains special characters: " . ($hasSpecialChars ? "YES" : "NO"));

// استفاده از سیستم جدید تأیید کلید2
if (!verifyMirrorServerKey($key2)) {
    debug_log("Invalid Mirror server key");
    respondJSON(['status' => 'error', 'message' => 'Invalid Mirror server key'], 401);
}

// NOTE: nonce verification is intentionally disabled for Mirror server compatibility
debug_log("INFO: nonce system is disabled for Mirror server compatibility");

// Check if data is provided
if (!$data || !isset($data['data']) || !is_array($data['data'])) {
    debug_log("No data provided or invalid data format");
    respondJSON(['status' => 'error', 'message' => 'No data provided or invalid data format'], 400);
}

// Process data updates
$updatedFields = updateUserData($user_id, $data['data']);

if ($updatedFields === false) {
    debug_log("Failed to update user data");
    respondJSON(['status' => 'error', 'message' => 'Failed to update user data'], 500);
}

// IMPORTANT: We don't use nonce system for Mirror server compatibility
debug_log("INFO: nonce system is disabled for Mirror server compatibility");

// Response with success
debug_log("Sending success response");
try {
    respondJSON([
        'status' => 'success',
        'message' => 'User data updated successfully',
        'updated_fields' => $updatedFields,
        'session_id' => $sessionId // اضافه کردن session_id در پاسخ
    ]);
} catch (Exception $e) {
    debug_log("ERROR in sending response: " . $e->getMessage());
    // Try simplified response in case of error
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success'
    ]);
}

/**
 * Updates user data
 * 
 * @param int $user_id The user ID
 * @param array $data The data to update
 * @return array|bool List of updated fields or false on error
 */
function updateUserData($user_id, $data) {
    global $conn;
    debug_log("Updating user data for user ID: $user_id");
    
    // List of valid fields that can be updated
    $valid_user_fields = ['balance', 'win_balance'];
    $valid_game_stats_fields = ['games_count', 'wins_count', 'losses_count'];
    
    $user_updates = [];
    $game_stats_updates = [];
    $updated_fields = [];
    
    // Process fields
    foreach ($data as $field => $value) {
        // Validate and sanitize field name
        $field = preg_replace('/[^a-zA-Z0-9_]/', '', $field);
        
        if (in_array($field, $valid_user_fields)) {
            // User table fields
            if ($field === 'balance') {
                $value = floatval($value);
                if ($value < 0) $value = 0; // Ensure balance is not negative
                $user_updates[] = "$field = $value";
                $updated_fields[] = $field;
            } else if ($field === 'win_balance') {
                $value = floatval($value);
                if ($value < 0) $value = 0; // Ensure win_balance is not negative
                $user_updates[] = "$field = $value";
                $updated_fields[] = $field;
            }
        } else if (in_array($field, $valid_game_stats_fields)) {
            // Game stats fields
            if (in_array($field, ['games_count', 'wins_count', 'losses_count'])) {
                $value = intval($value);
                if ($value < 0) $value = 0; // Ensure counts are not negative
                $game_stats_updates[] = "$field = $value";
                $updated_fields[] = $field;
            }
        }
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        $success = true;
        
        // Update user table if needed
        if (!empty($user_updates)) {
            $sql = "UPDATE users SET " . implode(', ', $user_updates) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $user_id);
            $result = $stmt->execute();
            
            if (!$result) {
                debug_log("Failed to update user table: " . $conn->error);
                $success = false;
            }
        }
        
        // Update game_stats table if needed
        if (!empty($game_stats_updates)) {
            // First check if game_stats record exists
            $stmt = $conn->prepare("SELECT user_id FROM game_stats WHERE user_id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                // Insert new record
                $fields = array_map(function($item) {
                    return explode(' = ', $item)[0];
                }, $game_stats_updates);
                
                $values = array_map(function($item) {
                    return explode(' = ', $item)[1];
                }, $game_stats_updates);
                
                $sql = "INSERT INTO game_stats (user_id, " . implode(', ', $fields) . ") VALUES (?, " . implode(', ', $values) . ")";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $user_id);
            } else {
                // Update existing record
                $sql = "UPDATE game_stats SET " . implode(', ', $game_stats_updates) . " WHERE user_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $user_id);
            }
            
            $result = $stmt->execute();
            
            if (!$result) {
                debug_log("Failed to update game_stats table: " . $conn->error);
                $success = false;
            }
        }
        
        if ($success) {
            $conn->commit();
            debug_log("User data updated successfully. Fields: " . implode(', ', $updated_fields));
            return $updated_fields;
        } else {
            $conn->rollback();
            debug_log("Rolling back transaction due to errors");
            return false;
        }
    } catch (Exception $e) {
        $conn->rollback();
        debug_log("Exception during update: " . $e->getMessage());
        return false;
    }
} 