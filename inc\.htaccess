# Prevent direct access to this directory
Order Deny,Allow
Deny from all

# Prevent PHP execution in this directory
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Hide file list
Options -Indexes

# Disable any script execution
Options -ExecCGI

# No access to .htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    Deny from all
</Files>

# Prevent access to all files
<FilesMatch ".*">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Block any remote file inclusion attempts
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{QUERY_STRING} ^.*(http|https|ftp)(:|\%3A)(/|\/|\%2F)(/|\/|\%2F).*$ [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Disable any display of errors
php_flag display_startup_errors off
php_flag display_errors off
php_flag html_errors off 