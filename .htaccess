# Secure .htaccess
<Files .htaccess>
    Order Allow,<PERSON>y
    Deny from all
</Files>

# Set default charset and language
AddDefaultCharset UTF-8
DefaultLanguage fa-IR

# Restrict access to API endpoints - ONLY Unity clients can access
<FilesMatch "\.php$">
    SetEnvIfNoCase User-Agent "Unity|UnityWebRequest|GoFasterGameClient" allow_access=1
    Order Deny,Allow
    Deny from all
    Allow from env=allow_access
    
    # Also allow localhost for development
    Allow from 127.0.0.1 ::1
</FilesMatch>

# Protected directories have additional restrictions
<Directory ~ "^inc">
    Order Deny,Allow
    Deny from all
</Directory>

# Prevent direct access to include files
<FilesMatch "^(db|security)\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent direct access to log files
<FilesMatch "\.(log|debug|error)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Prevent script execution in uploads directory
<Directory ~ "uploads">
    Options -ExecCGI
    AddHandler cgi-script .php .php3 .php4 .php5 .phtml .pl .py .jsp .asp .htm .html .shtml
    Order Allow,Deny
    Deny from all
</Directory>

# Prevent access to sensitive files
<FilesMatch "^(README|LICENSE|composer\.json|composer\.lock|package\.json|package-lock\.json|\.)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Disable showing PHP errors
php_flag display_startup_errors off
php_flag display_errors off
php_flag html_errors off
php_flag log_errors on
php_value error_log logs/php_error.log

# PHP security headers
<IfModule mod_headers.c>
    # Protect against XSS attacks
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME-sniffing
    Header set X-Content-Type-Options "nosniff"
    
    # Clickjacking protection
    Header set X-Frame-Options "SAMEORIGIN"
    
    # Content Security Policy - adjusted for API
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline';"
    
    # Referrer policy
    Header set Referrer-Policy "same-origin"
    
    # Feature policy
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # HSTS (Strict Transport Security)
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Cache control
    Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
    Header set Pragma "no-cache"
    Header set Expires "0"
</IfModule>

# Set timezone for all PHP files
<IfModule mod_php.c>
    php_value date.timezone "Asia/Tehran"
</IfModule>

# Block access to hidden files and directories except .well-known
<FilesMatch "^\.(?!well-known\/)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Restrict HTTP methods
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} !^(GET|POST|OPTIONS)$ [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Set CORS headers for API endpoints - only for Unity clients
<IfModule mod_headers.c>
    # Allow cross-origin access for our API - but only for specific clients
    SetEnvIfNoCase User-Agent "Unity|UnityWebRequest|GoFasterGameClient" allow_cors=1
    Header set Access-Control-Allow-Origin "*" env=allow_cors
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS" env=allow_cors
    Header set Access-Control-Allow-Headers "Authorization, Content-Type" env=allow_cors
    Header set Access-Control-Max-Age "86400" env=allow_cors
    
    # Special handling for OPTIONS requests (preflight)
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Prevent downloading PHP files
<FilesMatch "\.php$">
    <IfModule mod_headers.c>
        Header set Content-Disposition "inline"
    </IfModule>
</FilesMatch>

# Protect against SQL injection and other attacks
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (;|<|>|'|"|\)|%0A|%0D|%22|%27|%3C|%3E|%00|0x00) [NC,OR]
    RewriteCond %{QUERY_STRING} union([^a]*a)+ll\s*select [NC,OR]
    RewriteCond %{QUERY_STRING} concat[^\(]*\( [NC,OR]
    RewriteCond %{QUERY_STRING} exec(\s|\+)+(s|x)p [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Deny access to backup and source files
<FilesMatch "(\.(bak|config|dist|inc|ini|log|sh|sql|swp|dist)|~)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Rate limiting for sensitive endpoints
<IfModule mod_rewrite.c>
    # Enable rate limiting only for specific endpoints
    RewriteRule ^(login|register|verify_stake|get_referral_status)\.php$ - [E=RATE_LIMIT:1]
    
    # Skip if IP is whitelisted
    RewriteCond %{REMOTE_ADDR} !^(127\.0\.0\.1|::1)$
    RewriteCond %{ENV:RATE_LIMIT} =1
    
    # Create tracking file in /tmp
    RewriteRule .* - [E=TRACK:/tmp/rate_%{REMOTE_ADDR}_%{REQUEST_URI}]
    
    # Create it if not exists
    RewriteCond %{ENV:TRACK} !=""
    RewriteCond %{ENV:TRACK} !-f
    RewriteRule .* - [F=1;touch=%{ENV:TRACK}]
    
    # Count requests
    RewriteCond %{ENV:TRACK} !=""
    RewriteCond %{ENV:TRACK} -f
    RewriteRule .* - [F=5;append=%{ENV:TRACK}]
    
    # Check frequency - 10 requests per minute max
    RewriteCond %{ENV:TRACK} !=""
    RewriteCond %{ENV:TRACK} -f
    RewriteCond %{ENV:TRACK} -s
    RewriteCond %{TIME} <$(($(date +%s -r %{ENV:TRACK}) + 60))
    RewriteCond %{F:5} >9
    RewriteRule .* - [F,L]
</IfModule>

# Secure cookies
<IfModule mod_php.c>
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_only_cookies 1
</IfModule>

# Disable TRACE and TRACK methods
RewriteEngine On
RewriteCond %{REQUEST_METHOD} ^TRACE
RewriteRule .* - [F]

# Force API access to be https only
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Custom error page for API access denied
ErrorDocument 403 '{"status":"error","message":"Access denied: This API is restricted to the game client only"}'
# Use this instead for a friendlier message to users
# ErrorDocument 403 "<html><head><title>Access Denied</title></head><body><h1>Access Denied</h1><p>This API can only be accessed from the game client.</p></body></html>" 