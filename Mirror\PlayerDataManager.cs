using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Mirror;

/// <summary>
/// Simple class to display and edit player data on the server side
/// Works with Mirror authenticated player connection
/// </summary>
public class PlayerDataManager : NetworkBehaviour
{
    [Header("References")]
    [SerializeField] private MirrorServerAuthenticator serverAuthenticator;
    
    [Header("UI Elements")]
    [SerializeField] private TextMeshProUGUI usernameText;
    [SerializeField] private TMP_InputField balanceInput;
    [SerializeField] private TMP_InputField winBalanceInput;
    [SerializeField] private TMP_InputField gamesCountInput;
    [SerializeField] private TMP_InputField winsCountInput;
    [SerializeField] private TMP_InputField lossesCountInput;
    [SerializeField] private Button saveButton;
    [SerializeField] private Button refreshButton;
    
    // The player's network connection
    private NetworkConnection playerConnection;
    
    
    private void Start()
    {
        // This should only be active on the server
        if (!isServer)
        {
            // Not disabling the whole GameObject, just this component
            Debug.Log("[DEBUG] PlayerDataManager - Not running on server, component disabled");
            enabled = false;
            return;
        }

               // Find server authenticator if not set
        if (serverAuthenticator == null)
        {
            serverAuthenticator = FindAnyObjectByType<MirrorServerAuthenticator>();
            Debug.Log("[DEBUG] PlayerDataManager - Server authenticator found: " + (serverAuthenticator != null));
        }
        
        Debug.Log("[DEBUG] PlayerDataManager - Start called on server");
        
        // Register button callbacks
        if (saveButton != null)
        {
            saveButton.onClick.AddListener(OnSaveButtonClicked);
            Debug.Log("[DEBUG] PlayerDataManager - Save button listener registered");
        }
        else
        {
            Debug.LogWarning("[DEBUG] PlayerDataManager - Save button is null");
        }
        
        if (refreshButton != null)
        {
            refreshButton.onClick.AddListener(RefreshPlayerData);
            Debug.Log("[DEBUG] PlayerDataManager - Refresh button listener registered");
        }
        else
        {
            Debug.LogWarning("[DEBUG] PlayerDataManager - Refresh button is null");
        }
    }
    
    /// <summary>
    /// Set the player connection this manager is handling
    /// </summary>
    public void SetPlayerConnection(NetworkConnection conn)
    {
        Debug.Log($"[DEBUG] PlayerDataManager - Setting player connection: {conn.ToString()}");
        playerConnection = conn;
        
        // Refresh data immediately
        Debug.Log("[DEBUG] PlayerDataManager - Refreshing player data after connection set");
        RefreshPlayerData();
    }
    
    /// <summary>
    /// Refresh the player data from the server authenticator
    /// </summary>
    public void RefreshPlayerData()
    {
        Debug.Log("[DEBUG] PlayerDataManager - RefreshPlayerData called");
        
        if (serverAuthenticator == null || playerConnection == null)
        {
            Debug.LogError("[DEBUG] PlayerDataManager - Cannot refresh data: " + 
                          (serverAuthenticator == null ? "Server authenticator is null. " : "") + 
                          (playerConnection == null ? "Player connection is null." : ""));
            return;
        }
            
        if (!serverAuthenticator.IsAuthenticated(playerConnection))
        {
            Debug.LogWarning($"[DEBUG] PlayerDataManager - Player with connection {playerConnection.ToString()} is not authenticated");
            return;
        }
        
        // Get player data from authenticator
        Debug.Log($"[DEBUG] PlayerDataManager - Getting user data for connection {playerConnection.ToString()}");
        var userData = serverAuthenticator.GetUserData(playerConnection);
        if (userData == null)
        {
            Debug.LogError($"[DEBUG] PlayerDataManager - Failed to get user data for connection {playerConnection.ToString()}");
            return;
        }
        
        Debug.Log($"[DEBUG] PlayerDataManager - Successfully retrieved user data for {userData.username}");
        
        // Update UI elements
        if (usernameText != null)
        {
            usernameText.text = userData.username;
            Debug.Log($"[DEBUG] PlayerDataManager - Updated username text: {userData.username}");
        }
        
        if (balanceInput != null)
        {
            balanceInput.text = userData.balance.ToString("F2");
            Debug.Log($"[DEBUG] PlayerDataManager - Updated balance input: {userData.balance:F2}");
        }
        
        if (winBalanceInput != null)
        {
            winBalanceInput.text = userData.win_balance.ToString("F2");
            Debug.Log($"[DEBUG] PlayerDataManager - Updated win balance input: {userData.win_balance:F2}");
        }
        
        if (gamesCountInput != null)
        {
            gamesCountInput.text = userData.games_count.ToString();
            Debug.Log($"[DEBUG] PlayerDataManager - Updated games count input: {userData.games_count}");
        }
        
        if (winsCountInput != null)
        {
            winsCountInput.text = userData.wins_count.ToString();
            Debug.Log($"[DEBUG] PlayerDataManager - Updated wins count input: {userData.wins_count}");
        }
        
        if (lossesCountInput != null)
        {
            lossesCountInput.text = userData.losses_count.ToString();
            Debug.Log($"[DEBUG] PlayerDataManager - Updated losses count input: {userData.losses_count}");
        }
        
        Debug.Log("[DEBUG] PlayerDataManager - All UI elements updated with user data");
    }
    
    /// <summary>
    /// Save the player data to the server
    /// </summary>
    private void OnSaveButtonClicked()
    {
        Debug.Log("[DEBUG] PlayerDataManager - Save button clicked");
        
        if (serverAuthenticator == null || playerConnection == null)
        {
            Debug.LogError("[DEBUG] PlayerDataManager - Cannot save data: " + 
                           (serverAuthenticator == null ? "Server authenticator is null. " : "") + 
                           (playerConnection == null ? "Player connection is null." : ""));
            return;
        }
            
        if (!serverAuthenticator.IsAuthenticated(playerConnection))
        {
            Debug.LogWarning($"[DEBUG] PlayerDataManager - Player with connection {playerConnection.ToString()} is not authenticated");
            return;
        }
        
        Debug.Log("[DEBUG] PlayerDataManager - Parsing input values for update");
        
        // Parse input values
        float balance = 0;
        float winBalance = 0;
        int gamesCount = 0;
        int winsCount = 0;
        int lossesCount = 0;
        
        if (balanceInput != null && !string.IsNullOrEmpty(balanceInput.text))
        {
            float.TryParse(balanceInput.text, out balance);
            Debug.Log($"[DEBUG] PlayerDataManager - Parsed balance: {balance}");
        }
        
        if (winBalanceInput != null && !string.IsNullOrEmpty(winBalanceInput.text))
        {
            float.TryParse(winBalanceInput.text, out winBalance);
            Debug.Log($"[DEBUG] PlayerDataManager - Parsed win balance: {winBalance}");
        }
        
        if (gamesCountInput != null && !string.IsNullOrEmpty(gamesCountInput.text))
        {
            int.TryParse(gamesCountInput.text, out gamesCount);
            Debug.Log($"[DEBUG] PlayerDataManager - Parsed games count: {gamesCount}");
        }
        
        if (winsCountInput != null && !string.IsNullOrEmpty(winsCountInput.text))
        {
            int.TryParse(winsCountInput.text, out winsCount);
            Debug.Log($"[DEBUG] PlayerDataManager - Parsed wins count: {winsCount}");
        }
        
        if (lossesCountInput != null && !string.IsNullOrEmpty(lossesCountInput.text))
        {
            int.TryParse(lossesCountInput.text, out lossesCount);
            Debug.Log($"[DEBUG] PlayerDataManager - Parsed losses count: {lossesCount}");
        }
        
        // Create data to update
        var dataToUpdate = new Dictionary<string, object>
        {
            { "balance", balance },
            { "win_balance", winBalance },
            { "games_count", gamesCount },
            { "wins_count", winsCount },
            { "losses_count", lossesCount }
        };
        
        Debug.Log($"[DEBUG] PlayerDataManager - Updating data for connection {playerConnection.ToString()} with fields: balance={balance}, win_balance={winBalance}, games_count={gamesCount}, wins_count={winsCount}, losses_count={lossesCount}");
        
        // Update user data
        serverAuthenticator.UpdateUserData(playerConnection, dataToUpdate);
        
        Debug.Log($"[DEBUG] PlayerDataManager - UpdateUserData called for connection {playerConnection.ToString()}");
        
        // Refresh UI after a short delay
        StartCoroutine(RefreshAfterDelay(1.0f));
    }
    
    // Refresh after a delay to ensure data is updated
    private IEnumerator RefreshAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        RefreshPlayerData();
    }
    
    /// <summary>
    /// Update specific player stats easily
    /// </summary>
    public void UpdatePlayerStats(bool gameCompleted, bool isWinner)
    {
        if (serverAuthenticator == null || playerConnection == null)
            return;
            
        if (!serverAuthenticator.IsAuthenticated(playerConnection))
        {
            Debug.LogWarning("Player is not authenticated");
            return;
        }
        
        // Get current stats
        var userData = serverAuthenticator.GetUserData(playerConnection);
        if (userData == null)
        {
            Debug.LogError("Failed to get user data");
            return;
        }
        
        int gamesCount = userData.games_count;
        int winsCount = userData.wins_count;
        int lossesCount = userData.losses_count;
        
        // Update stats based on game result
        if (gameCompleted)
        {
            gamesCount++;
            
            if (isWinner)
            {
                winsCount++;
            }
            else
            {
                lossesCount++;
            }
        }
        
        // Create data to update
        var dataToUpdate = new Dictionary<string, object>
        {
            { "games_count", gamesCount },
            { "wins_count", winsCount },
            { "losses_count", lossesCount }
        };
        
        // Update user data
        serverAuthenticator.UpdateUserData(playerConnection, dataToUpdate);
        
        // Refresh UI if visible
        RefreshPlayerData();
    }
} 