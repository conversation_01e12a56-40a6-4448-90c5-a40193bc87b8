<?php
// Reset password page
session_start();

// Include database connection
require_once 'inc/db.php';

// Function to validate token
function validateToken($token, $conn) {
    try {
        // Check if token exists and is not expired
        $stmt = $conn->prepare("SELECT user_id, expiration FROM password_reset_tokens WHERE token = ?");
        if (!$stmt) {
            error_log("Prepare failed: " . $conn->error);
            return [
                'valid' => false,
                'message' => 'Database error. Please try again.'
            ];
        }

        $stmt->bind_param("s", $token);
        $success = $stmt->execute();

        if (!$success) {
            error_log("Execute failed: " . $stmt->error);
            return [
                'valid' => false,
                'message' => 'Database error. Please try again.'
            ];
        }

        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return [
                'valid' => false,
                'message' => 'Invalid or expired recovery link'
            ];
        }

        $row = $result->fetch_assoc();
        $expiration = strtotime($row['expiration']);
        $now = time();

        if ($now > $expiration) {
            return [
                'valid' => false,
                'message' => 'Recovery link has expired'
            ];
        }

        return [
            'valid' => true,
            'user_id' => $row['user_id']
        ];
    } catch (Exception $e) {
        error_log("Error validating token: " . $e->getMessage());
        return [
            'valid' => false,
            'message' => 'System error. Please try again later.'
        ];
    }
}

// Function to update password
function updatePassword($user_id, $password, $conn) {
    try {
        // Hash the password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Update the user's password
        $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        if (!$stmt) {
            error_log("Prepare failed: " . $conn->error);
            return false;
        }

        $stmt->bind_param("si", $hashed_password, $user_id);
        $result = $stmt->execute();

        if ($result) {
            // Delete the used token
            $stmt = $conn->prepare("DELETE FROM password_reset_tokens WHERE user_id = ?");
            if ($stmt) {
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
            }

            // Log the successful password change
            error_log("Password successfully changed for user ID: $user_id");
        } else {
            error_log("Failed to update password for user ID: $user_id - " . $stmt->error);
        }

        return $result;
    } catch (Exception $e) {
        error_log("Error updating password: " . $e->getMessage());
        return false;
    }
}

// Initialize variables
$token = isset($_GET['token']) ? $_GET['token'] : '';
$message = '';
$messageType = '';
$showForm = false;

// Process the token validation
if (!empty($token)) {
    $validation = validateToken($token, $conn);

    if ($validation['valid']) {
        $showForm = true;
        $_SESSION['reset_user_id'] = $validation['user_id'];
        $_SESSION['reset_token'] = $token;
    } else {
        $message = $validation['message'];
        $messageType = 'error';
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['password']) && isset($_POST['confirm_password'])) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate password
    if (strlen($password) < 8) {
        $message = 'Password must be at least 8 characters long';
        $messageType = 'error';
        $showForm = true;
    } elseif ($password !== $confirm_password) {
        $message = 'Passwords do not match';
        $messageType = 'error';
        $showForm = true;
    } else {
        // Update password
        if (isset($_SESSION['reset_user_id']) && isset($_SESSION['reset_token'])) {
            $user_id = $_SESSION['reset_user_id'];

            // Validate token again for security
            $validation = validateToken($_SESSION['reset_token'], $conn);

            if ($validation['valid']) {
                $result = updatePassword($user_id, $password, $conn);

                if ($result) {
                    $message = 'Password has been successfully changed. You can now log in with your new password';
                    $messageType = 'success';
                    $showForm = false;

                    // Clear session variables
                    unset($_SESSION['reset_user_id']);
                    unset($_SESSION['reset_token']);
                } else {
                    $message = 'Error updating password. Please try again';
                    $messageType = 'error';
                    $showForm = true;
                }
            } else {
                $message = 'Invalid or expired recovery link';
                $messageType = 'error';
                $showForm = false;
            }
        } else {
            $message = 'Session error. Please try again';
            $messageType = 'error';
            $showForm = false;
        }
    }
}

// Close the database connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Recovery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }
        button:hover {
            background-color: #45a049;
        }
        .message {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .app-link {
            text-align: center;
            margin-top: 20px;
        }
        .app-link a {
            color: #4CAF50;
            text-decoration: none;
        }
        .app-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Password Recovery</h1>

        <?php if (!empty($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($showForm): ?>
            <form method="post" action="">
                <div class="form-group">
                    <label for="password">New Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm New Password:</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>

                <button type="submit">Change Password</button>
            </form>
        <?php elseif ($messageType === 'success'): ?>
            <div class="app-link">
                <p>Come back to the game and login.</p>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
