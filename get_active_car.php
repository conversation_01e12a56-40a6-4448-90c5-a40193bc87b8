<?php
/**
 * API endpoint to get the active car for the authenticated user
 */

// Include required files
require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if the request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

try {
    // Get token from Authorization header
    $token = getBearerToken();

    if (!$token) {
        respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
    }

    // Verify token and get user ID
    $user_id = verifyJWT($token);

    if (!$user_id) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
    }

    // Generate a new nonce for security
    $new_nonce = generateNonce($user_id);

    // Get the active car for the user
    $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ? AND is_active = 1 LIMIT 1");
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // User has an active car
        $row = $result->fetch_assoc();
        $active_car = $row['car_id'];

        // Return the active car with full car data
        respondJSON([
            'status' => 'success',
            'active_car' => $active_car,
            'car_data' => $row,
            'nonce' => $new_nonce
        ]);
    } else {
        // Check if the user has any cars
        $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ? LIMIT 1");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // User has at least one car, but none is active
            // Return the first car
            $row = $result->fetch_assoc();
            $first_car = $row['car_id'];

            // Activate this car
            $active = 1;
            $stmt = $conn->prepare("UPDATE user_cars SET is_active = ? WHERE user_id = ? AND car_id = ?");
            $stmt->bind_param('iis', $active, $user_id, $first_car);
            $stmt->execute();

            // Return the activated car with full car data
            respondJSON([
                'status' => 'success',
                'active_car' => $first_car,
                'car_data' => $row,
                'message' => 'No active car found, activated the first available car',
                'nonce' => $new_nonce
            ]);
        } else {
            // User has no cars, check if there's a free car
            $stmt = $conn->prepare("SELECT id FROM cars WHERE price = 0 LIMIT 1");
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                // There's a free car, give it to the user
                $row = $result->fetch_assoc();
                $free_car_id = $row['id'];

                // Format the car ID as expected by the client
                $car_id = "Car:" . $free_car_id;

                // Add the free car to the user's cars
                $active = 1;
                $stmt = $conn->prepare("INSERT INTO user_cars (user_id, car_id, is_active) VALUES (?, ?, ?)");
                $stmt->bind_param('isi', $user_id, $car_id, $active);
                $stmt->execute();

                // Get the newly added car data
                $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ? AND car_id = ? LIMIT 1");
                $stmt->bind_param('is', $user_id, $car_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();

                // Return the free car with full car data
                respondJSON([
                    'status' => 'success',
                    'active_car' => $car_id,
                    'car_data' => $row,
                    'message' => 'No cars found, added a free car',
                    'nonce' => $new_nonce
                ]);
            } else {
                // No cars available, return an error
                respondJSON([
                    'status' => 'error',
                    'message' => 'No cars available',
                    'nonce' => $new_nonce
                ], 404);
            }
        }
    }

} catch (Exception $e) {
    // Return an error
    respondJSON([
        'status' => 'error',
        'message' => 'Server error: ' . $e->getMessage()
    ], 500);
}
?>
