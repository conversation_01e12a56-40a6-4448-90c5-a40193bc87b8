<?php
// Set timezone for consistent date/time operations
date_default_timezone_set('Asia/Tehran');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Log file for debugging
$log_file = 'logs/verify_for_mirror.log';

// Enable error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', $log_file);

function debug_log($message) {
    global $log_file;
    
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false || 
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false ||
        strpos(strtolower($message), 'warning') !== false) {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    }
}

// Disabled regular logging
// debug_log("API Request received: verify_for_mirror.php");

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

debug_log("Input data: " . json_encode($input));

// Validate required fields
if (!isset($input['token']) || !isset($input['key1'])) {
    debug_log("Missing required fields");
    respondJSON(['status' => 'error', 'message' => 'Token and Key1 are required'], 400);
}

$token = $input['token'];
$key1 = $input['key1'];
$sessionId = isset($input['session_id']) ? $input['session_id'] : null;

debug_log("Session ID received: " . ($sessionId ? $sessionId : "Not provided"));

// اضافه کردن بررسی session_id
if (empty($sessionId)) {
    debug_log("ERROR: Session ID is empty or not provided");
    respondJSON(['status' => 'error', 'message' => 'Session ID is required'], 400);
}

// Verify token
$user_id = verifyJWT($token);

// If no valid user ID, return error
if (!$user_id) {
    error_log("Failed to verify token for key2 request");
    $response = [
        'success' => false,
        'message' => 'Invalid or expired token'
    ];
    echo json_encode($response);
    exit;
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    error_log("Extracted user_id from array: " . $user_id);
}

// Check rate limit
if (!checkRateLimit($user_id)) {
    debug_log("Rate limit exceeded for user: $user_id");
    respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
}

// Verify Key1
if (!verifyUserKey($user_id, $key1)) {
    debug_log("Invalid or expired Key1");
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
}

// Get user data
debug_log("Getting user data for user ID: $user_id");
$stmt = $conn->prepare("
    SELECT 
        u.username, 
        u.email, 
        u.wallet_address, 
        u.balance,
        u.win_balance,
        gs.games_count,
        gs.wins_count,
        gs.losses_count,
        gs.cars_data
    FROM 
        users u
    LEFT JOIN 
        game_stats gs ON u.id = gs.user_id
    WHERE 
        u.id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    debug_log("User not found in database");
    respondJSON(['status' => 'error', 'message' => 'User not found'], 404);
}

$user_data = $result->fetch_assoc();
debug_log("User data retrieved: " . json_encode($user_data));

// Prepare cars_data
$cars_data = $user_data['cars_data'] ? json_decode($user_data['cars_data'], true) : [];

// Generate Key2 for Mirror server to use for editing
$key2 = generateUserKey2($user_id, $sessionId);
debug_log("Generated user-specific key2 for user: $user_id with session: $sessionId");

// Check if there's an issue with key generation or disconnect request
if ($key2 === false || checkDisconnectRequest($user_id, $sessionId)) {
    debug_log("Disconnect required for user: $user_id with session: $sessionId");
    respondJSON([
        'status' => 'error',
        'message' => 'disconnect_required',
        'user_id' => $user_id,
        'session_id' => $sessionId
    ], 200); // کد 200 استفاده می‌شود تا پاسخ به درستی توسط کلاینت پردازش شود
}

// Return user data and Key2
debug_log("Sending response with user data and Key2");
respondJSON([
    'status' => 'success',
    'user_id' => $user_id,
    'user' => [
        'username' => $user_data['username'],
        'email' => $user_data['email'],
        'wallet_address' => $user_data['wallet_address'],
        'balance' => (float)$user_data['balance'],
        'win_balance' => (float)$user_data['win_balance'],
        'games_count' => (int)$user_data['games_count'],
        'wins_count' => (int)$user_data['wins_count'],
        'losses_count' => (int)$user_data['losses_count'],
        'cars_data' => $cars_data
    ],
    'key2' => $key2,
    'session_id' => $sessionId // اضافه کردن session_id در پاسخ
]); 