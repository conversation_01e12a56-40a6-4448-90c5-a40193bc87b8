using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;
using TMPro;
using System.Collections.Generic;
using Newtonsoft.Json;

public class GetUserData : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI usernameText;
    [SerializeField] private TextMeshP<PERSON>UGUI emailText;
    [SerializeField] private TextMeshProUGUI balanceText;
    [SerializeField] private TextMeshProUGUI winBalanceText;
    [SerializeField] private TextMeshProUGUI gamesCountText;
    [SerializeField] private TextMeshProUGUI winsCountText;
    [SerializeField] private TextMeshProUGUI lossesCountText;
    [SerializeField] private TextMeshP<PERSON>UGUI walletAddressText;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private UnityEngine.UI.Button refreshButton;
    [SerializeField] private UnityEngine.UI.Button refreshButton2;
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/get_user_data.php";
    [SerializeField] private GameObject sessionManagerPrefab;

    // User data model
    [Serializable]
    public class UserData
    {
        public string username;
        public string email;
        public string wallet_address;
        public float balance;
        public float win_balance;
        public int games_count;
        public int wins_count;
        public int losses_count;
        public List<string> cars_data;
    }

    [Serializable]
    private class UserDataResponse
    {
        public string status;
        public UserData user;
        public string nonce;
        public int token_expires_in; // 24 hours in seconds
        public string token_type; // "Bearer"
    }

    private void Start()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
            Debug.Log("SessionManager instantiated from GetUserData");
        }

        // Set up refresh button onClick listeners
        if (refreshButton != null)
        {
            refreshButton.onClick.AddListener(OnRefreshButtonClick);
        }

        if (refreshButton2 != null)
        {
            refreshButton2.onClick.AddListener(OnRefreshButton2Click);
        }

        // Automatically fetch user data on start if logged in
        CheckLoginStatusAndFetchData();
    }

    // Method called when refresh button is clicked
    public void OnRefreshButtonClick()
    {
        // Refresh user data from server
        CheckLoginStatusAndFetchData();
    }

    // Method called when second refresh button is clicked
    public void OnRefreshButton2Click()
    {
        // Refresh user data from server
        CheckLoginStatusAndFetchData();
    }

    private void CheckLoginStatusAndFetchData()
    {
        // Check login status using SessionManager first, then fallback to Login static properties
        bool isLoggedIn = false;

        if (SessionManager.Instance != null)
        {
            isLoggedIn = SessionManager.Instance.IsLoggedIn;
        }
        else
        {
            // Fallback to checking static Login properties
            isLoggedIn = !string.IsNullOrEmpty(Login.JWT_Token) && !string.IsNullOrEmpty(Login.Key1);
        }

        if (isLoggedIn)
        {
            FetchUserData();
        }
        else
        {
            statusText.text = "You need to log in first";

            // Clear data fields when not logged in
            ClearUserDataDisplay();
        }
    }

    private void ClearUserDataDisplay()
    {
        if (usernameText != null) usernameText.text = "-";
        if (emailText != null) emailText.text = "-";
        if (balanceText != null) balanceText.text = "-";
        if (winBalanceText != null) winBalanceText.text = "-";
        if (gamesCountText != null) gamesCountText.text = "-";
        if (winsCountText != null) winsCountText.text = "-";
        if (lossesCountText != null) lossesCountText.text = "-";
        if (walletAddressText != null) walletAddressText.text = "-";
    }

    public void FetchUserData()
    {
        StartCoroutine(GetUserDataCoroutine());
    }

    private IEnumerator GetUserDataCoroutine()
    {
        string token, key1, nonce;

        // Get authentication data from SessionManager if available, otherwise fallback to static properties
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            token = SessionManager.Instance.JWT_Token;
            key1 = SessionManager.Instance.Key1;
            nonce = SessionManager.Instance.CurrentNonce;

            // With 24-hour token system, we might not have a nonce yet, but that's okay
            // The server will generate one for us if needed
            if (string.IsNullOrEmpty(nonce))
            {
                Debug.Log("No nonce available, but using 24-hour token system");
                // We'll proceed without a nonce, the server will handle it
            }
        }
        else
        {
            token = Login.JWT_Token;
            key1 = Login.Key1;
            nonce = Login.CurrentNonce;
        }

        // Check if we have token and key (nonce is optional with 24-hour token system)
        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1))
        {
            statusText.text = "Not logged in or missing credentials";
            yield break;
        }

        // Prepare the request URL with query parameters
        string requestUrl;
        if (!string.IsNullOrEmpty(nonce))
        {
            requestUrl = $"{apiUrl}?key1={UnityWebRequest.EscapeURL(key1)}&nonce={UnityWebRequest.EscapeURL(nonce)}";
        }
        else
        {
            // With 24-hour token system, we can make requests without a nonce
            requestUrl = $"{apiUrl}?key1={UnityWebRequest.EscapeURL(key1)}";
        }

        Debug.Log($"Using 24-hour token system - Request URL: {requestUrl}");

        // Create the request
        UnityWebRequest request = UnityWebRequest.Get(requestUrl);

        // Add authorization header
        request.SetRequestHeader("Authorization", "Bearer " + token);

        // Send the request
        statusText.text = "Fetching user data...";
        yield return request.SendWebRequest();

        // Handle response
        if (request.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError("Get User Data Error: " + request.error);
            Debug.LogError("Response Code: " + request.responseCode);
            Debug.LogError("Response Headers: " + request.GetResponseHeaders());
            Debug.LogError("Response Text: " + request.downloadHandler.text);
            statusText.text = "Failed to fetch user data: " + request.error;
            yield break;
        }

        try
        {
            // Parse response
            string responseText = request.downloadHandler.text;
            Debug.Log("Raw API Response: " + responseText);

            // حذف هشدارهای PHP از پاسخ
            int jsonStartIndex = responseText.IndexOf('{');
            if (jsonStartIndex > 0)
            {
                responseText = responseText.Substring(jsonStartIndex);
            }

            // استفاده از Newtonsoft.Json برای پارس کردن پاسخ
            UserDataResponse response = JsonConvert.DeserializeObject<UserDataResponse>(responseText);

            if (response != null && response.status == "success")
            {
                // Log received user data for debugging
                Debug.Log($"Received user data - Username: {response.user.username}, Email: {response.user.email}, " +
                          $"Balance: {response.user.balance}, Win Balance: {response.user.win_balance}, " +
                          $"Games: {response.user.games_count}, Wins: {response.user.wins_count}, " +
                          $"Losses: {response.user.losses_count}");

                // Update UI
                if (usernameText != null) usernameText.text = response.user.username;
                if (emailText != null) emailText.text = response.user.email;
                if (balanceText != null) balanceText.text = $"{response.user.balance}";
                if (winBalanceText != null) winBalanceText.text = $"{response.user.win_balance}";
                if (gamesCountText != null) gamesCountText.text = $"{response.user.games_count}";
                if (winsCountText != null) winsCountText.text = $"{response.user.wins_count}";
                if (lossesCountText != null) lossesCountText.text = $"{response.user.losses_count}";
                if (walletAddressText != null) walletAddressText.text = response.user.wallet_address;

                // Store the new nonce in SessionManager
                if (SessionManager.Instance != null)
                {
                    SessionManager.Instance.UpdateNonce(response.nonce);
                }
                else
                {
                    // Fallback to static property
                    Login.CurrentNonce = response.nonce;
                }

                statusText.text = "User data updated successfully";
                Debug.Log("User data fetched successfully");
            }
            else
            {
                statusText.text = "Failed to fetch user data";
                Debug.LogError("Failed to fetch user data: " + responseText);
            }
        }
        catch (Exception ex)
        {
            statusText.text = "Error parsing user data";
            Debug.LogError("Error parsing user data response: " + ex.Message);
        }
    }
}