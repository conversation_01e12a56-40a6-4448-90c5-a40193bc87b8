<?php
// Error handler at the very beginning of the file
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno]: $errstr in $errfile on line $errline");
    // For Unity compatibility, always return JSO<PERSON>
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'An internal error occurred. Please try again later.',
        'error_code' => 'SERVER_ERROR'
    ]);
    exit(0); // Exit with success status for Unity
});

// Exception handler
set_exception_handler(function($exception) {
    error_log("Uncaught Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    // For Unity compatibility, always return JSON
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'An internal error occurred. Please try again later.',
        'error_code' => 'SERVER_EXCEPTION'
    ]);
    exit(0); // Exit with success status for Unity
});

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Fatal Error: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);
        // Headers may already be sent in case of fatal error, but try anyway
        if (!headers_sent()) {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'A fatal error occurred. Please try again later.',
                'error_code' => 'FATAL_ERROR'
            ]);
        }
    }
});

// Basic validation check for db.php and security.php - avoid include failures
if (!file_exists('inc/db.php') || !file_exists('inc/security.php')) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Configuration error. Please contact support.',
        'error_code' => 'CONFIG_ERROR'
    ]);
    exit(0);
}

// Includes inside try-catch
try {
    require_once 'inc/db.php';
    require_once 'inc/security.php';
} catch (Exception $e) {
    error_log("Include error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'System configuration error. Please contact support.',
        'error_code' => 'INCLUDE_ERROR'
    ]);
    exit(0);
}

// Set timezone and other settings
date_default_timezone_set('Asia/Tehran');
ini_set('display_errors', 0);
ini_set('log_errors', 0);
ini_set('error_log', 'login_errors.log');

// Safe response function - always returns HTTP 200
function respondWithJson($data) {
    header('Content-Type: application/json');
    // Add timestamp to responses
    $data['timestamp'] = date('Y-m-d H:i:s');
    echo json_encode($data);
    exit(0); // Always exit with success status for Unity
}

// Check if login_attempts table exists
function ensureLoginAttemptsTable($conn) {
    try {
        $result = $conn->query("SHOW TABLES LIKE 'login_attempts'");
        if ($result->num_rows == 0) {
            $conn->query("CREATE TABLE IF NOT EXISTS `login_attempts` (
                `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                `ip_address` varchar(45) NOT NULL,
                `attempt_count` int(11) NOT NULL DEFAULT 1,
                `last_attempt` timestamp NOT NULL DEFAULT current_timestamp(),
                UNIQUE KEY `ip_address` (`ip_address`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        }
        return true;
    } catch (Exception $e) {
        error_log("Could not ensure login_attempts table: " . $e->getMessage());
        return false;
    }
}

// Function to increment login attempt counter - simplified
function incrementLoginAttempt($conn, $ip) {
    try {
        // First, ensure the table exists
        if (!ensureLoginAttemptsTable($conn)) {
            return false;
        }

        // Try to update existing record first
        $stmt = $conn->prepare("UPDATE login_attempts SET attempt_count = attempt_count + 1, last_attempt = NOW() WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();

        // If no rows affected, insert new record
        if ($stmt->affected_rows == 0) {
            $stmt = $conn->prepare("INSERT INTO login_attempts (ip_address, attempt_count) VALUES (?, 1)");
            $stmt->bind_param("s", $ip);
            $stmt->execute();
        }

        // Get the current count for logging purposes
        $stmt = $conn->prepare("SELECT attempt_count FROM login_attempts WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $attempt_count = $row['attempt_count'];

            return $attempt_count;
        } else {
            return 1; // Fallback value
        }
    } catch (Exception $e) {
        error_log("Error incrementing login attempt: " . $e->getMessage());
        return false;
    }
}

// Function to check login rate limit - simplified
function checkLoginRateLimit($conn, $ip) {
    try {
        // First, ensure the table exists
        if (!ensureLoginAttemptsTable($conn)) {
            return true; // Allow login if we can't check
        }

        // Reset counters older than 15 minutes
        $conn->query("UPDATE login_attempts SET attempt_count = 1 WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 15 MINUTE)");

        // Get current count
        $stmt = $conn->prepare("SELECT attempt_count FROM login_attempts WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $attempt_count = $row['attempt_count'];

            // Maximum 5 attempts per 15 minutes
            if ($attempt_count >= 5) {
                error_log("Login rate limit exceeded for IP: $ip ($attempt_count attempts)");
                return false;
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Error checking login rate limit: " . $e->getMessage());
        return true; // Allow login if we can't check
    }
}

// Test connection immediately
if (!isset($conn) || $conn->connect_error) {
    error_log("Database connection failed: " . ($conn->connect_error ?? "Unknown error"));
    respondWithJson([
        'status' => 'error',
        'message' => 'Database connection error. Please try again later.',
        'error_code' => 'DB_CONNECT_ERROR'
    ]);
}

// == MAIN LOGIN LOGIC ==
// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    respondWithJson([
        'status' => 'error',
        'message' => 'Method not allowed',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
}

// Get IP for rate limiting
$ip_address = $_SERVER['REMOTE_ADDR'];

// Check rate limiting
if (!checkLoginRateLimit($conn, $ip_address)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Too many login attempts. Please try again later.',
        'error_code' => 'RATE_LIMIT_EXCEEDED'
    ]);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

// Validate required fields
if (!isset($input['username']) || !isset($input['password'])) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Username and password are required',
        'error_code' => 'MISSING_CREDENTIALS'
    ]);
}

$username = trim($input['username']);
$password = trim($input['password']);

// Validate input
if (empty($username) || empty($password)) {
    respondWithJson([
        'status' => 'error',
        'message' => 'Username and password cannot be empty',
        'error_code' => 'EMPTY_CREDENTIALS'
    ]);
}

// Increment login attempt before checking
incrementLoginAttempt($conn, $ip_address);

// Get user data
try {
    $stmt = $conn->prepare("SELECT id, password, status FROM users WHERE username = ?");
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        respondWithJson([
            'status' => 'error',
            'message' => 'Login service error. Please try again later.',
            'error_code' => 'DB_PREPARE_ERROR'
        ]);
    }

    $stmt->bind_param("s", $username);
    $success = $stmt->execute();

    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        respondWithJson([
            'status' => 'error',
            'message' => 'Login service error. Please try again later.',
            'error_code' => 'DB_EXECUTE_ERROR'
        ]);
    }

    $result = $stmt->get_result();

    // Check if user exists
    if ($result->num_rows === 0) {
        respondWithJson([
            'status' => 'error',
            'message' => 'Invalid username or password',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
    }

    $user = $result->fetch_assoc();

    // Check if user is active
    if ($user['status'] !== 'active') {
        respondWithJson([
            'status' => 'error',
            'message' => 'Account is not active',
            'error_code' => 'INACTIVE_ACCOUNT'
        ]);
    }

    // Verify password
    if (!password_verify($password, $user['password'])) {
        respondWithJson([
            'status' => 'error',
            'message' => 'Invalid username or password',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
    }

    // Login successful from here
    $user_id = $user['id'];

    // Cleanup old tokens and generate new ones
    try {
        // پاکسازی توکن‌ها و کلیدهای قبلی برای جلوگیری از انباشته شدن
        cleanupOldTokens($user_id);
        cleanupOldKeys($user_id);
        cleanupOldNonces($user_id);

        // در هر بار لاگین، یک JWT_SECRET جدید برای کاربر تولید می‌کنیم
        $jwt_secret = refreshUserJwtSecret($user_id);

        // Generate JWT token with the new secret
        $token = generateJWT($user_id);

        // Store token in database
        storeUserToken($user_id, $token);

        // Generate Key1 for data retrieval
        $key1 = generateRandomKey();
        storeUserKey($user_id, $key1);

        // Update last login time
        $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();

        // Generate nonce for first request
        $nonce = generateNonce($user_id);

        // Return success response with 24-hour token
        respondWithJson([
            'status' => 'success',
            'message' => 'Login successful',
            'token' => $token,
            'key1' => $key1,
            'nonce' => $nonce,
            'token_expires_in' => 24 * 60 * 60, // 24 hours in seconds
            'token_type' => 'Bearer'
        ]);
    } catch (Exception $e) {
        error_log("Token generation error: " . $e->getMessage());
        respondWithJson([
            'status' => 'error',
            'message' => 'Login successful but failed to generate session tokens. Please try again.',
            'error_code' => 'TOKEN_GENERATION_ERROR'
        ]);
    }
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    respondWithJson([
        'status' => 'error',
        'message' => 'Login service error. Please try again later.',
        'error_code' => 'LOGIN_PROCESS_ERROR'
    ]);
}