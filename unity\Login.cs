using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;
using TMPro;

public class Login : MonoBehaviour
{
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private TMP_InputField passwordInput;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/login.php";

    [SerializeField] private UnityEngine.UI.Toggle rememberMeToggle; // Toggle برای ذخیره نام کاربری و رمز عبور
    [SerializeField] private UnityEngine.UI.Button exitGameButton; // دکمه خروج از بازی
    [SerializeField] private UnityEngine.UI.Button togglePasswordButton; // دکمه برای نمایش/مخفی کردن رمز عبور

    // Store token and key - kept for backward compatibility
    public static string JWT_Token { get; set; }
    public static string Key1 { get; set; }
    public static string CurrentNonce { get; set; }

    [SerializeField] private GameObject sessionManagerPrefab;

    private void Awake()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
        }
    }

    private void Start()
    {
        // Show login status
        UpdateLoginStatus();

        // اضافه کردن لیسنر برای دکمه خروج از بازی
        if (exitGameButton != null)
        {
            exitGameButton.onClick.AddListener(OnExitGameButtonClick);
        }

        // اضافه کردن لیسنر برای دکمه نمایش/مخفی کردن رمز عبور
        if (togglePasswordButton != null)
        {
            togglePasswordButton.onClick.AddListener(OnTogglePasswordButtonClick);
        }

        // بازیابی اطلاعات ذخیره شده اگر Toggle فعال بوده است
        if (rememberMeToggle != null)
        {
            // بازیابی وضعیت Toggle
            rememberMeToggle.isOn = PlayerPrefs.GetInt("RememberMe", 0) == 1;

            // اگر Toggle فعال است، اطلاعات ذخیره شده را بازیابی کنیم
            if (rememberMeToggle.isOn)
            {
                if (usernameInput != null)
                {
                    usernameInput.text = PlayerPrefs.GetString("SavedUsername", "");
                }

                if (passwordInput != null)
                {
                    passwordInput.text = PlayerPrefs.GetString("SavedPassword", "");
                }
            }
        }
    }

    public void OnLoginButtonClick()
    {
        // ذخیره اطلاعات لاگین اگر Toggle فعال است
        SaveLoginInfo();

        // شروع فرآیند لاگین
        StartCoroutine(LoginCoroutine());
    }

    public void OnLogoutButtonClick()
    {
        if (SessionManager.Instance != null)
        {
            SessionManager.Instance.Logout();
            UpdateLoginStatus();
        }
    }

    // متد برای خروج از بازی
    public void OnExitGameButtonClick()
    {
        Debug.Log("Exiting game...");

        // ذخیره اطلاعات اگر Toggle فعال است
        SaveLoginInfo();

        // خروج از بازی
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }

    // متد برای نمایش/مخفی کردن رمز عبور
    public void OnTogglePasswordButtonClick()
    {
        if (passwordInput != null)
        {
            // تغییر وضعیت نمایش رمز عبور
            passwordInput.contentType = (passwordInput.contentType == TMP_InputField.ContentType.Password)
                ? TMP_InputField.ContentType.Standard
                : TMP_InputField.ContentType.Password;

            // به‌روزرسانی فیلد ورودی
            passwordInput.ForceLabelUpdate();

            // تمرکز مجدد روی فیلد (اختیاری)
            passwordInput.Select();

            Debug.Log("Password visibility toggled: " + (passwordInput.contentType == TMP_InputField.ContentType.Standard ? "Visible" : "Hidden"));
        }
    }

    // متد برای ذخیره اطلاعات لاگین
    private void SaveLoginInfo()
    {
        if (rememberMeToggle != null)
        {
            // ذخیره وضعیت Toggle
            PlayerPrefs.SetInt("RememberMe", rememberMeToggle.isOn ? 1 : 0);

            // اگر Toggle فعال است، اطلاعات را ذخیره کنیم
            if (rememberMeToggle.isOn)
            {
                if (usernameInput != null)
                {
                    PlayerPrefs.SetString("SavedUsername", usernameInput.text);
                }

                if (passwordInput != null)
                {
                    PlayerPrefs.SetString("SavedPassword", passwordInput.text);
                }
            }
            else
            {
                // اگر Toggle غیرفعال است، اطلاعات ذخیره شده را پاک کنیم
                PlayerPrefs.DeleteKey("SavedUsername");
                PlayerPrefs.DeleteKey("SavedPassword");
            }

            // ذخیره تغییرات
            PlayerPrefs.Save();
        }
    }

    private void UpdateLoginStatus()
    {
        // Clear any existing login data when the login screen is shown
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            // Log out the user automatically when they reach the login screen
            SessionManager.Instance.Logout();
        }

        // Always show "Please log in" message
        statusText.text = "Please log in";

        // بازیابی اطلاعات ذخیره شده اگر Toggle فعال است
        if (rememberMeToggle != null && rememberMeToggle.isOn)
        {
            if (usernameInput != null)
            {
                usernameInput.text = PlayerPrefs.GetString("SavedUsername", "");
            }

            if (passwordInput != null)
            {
                passwordInput.text = PlayerPrefs.GetString("SavedPassword", "");
            }
        }
        else
        {
            // Clear input fields if they exist
            if (usernameInput != null) usernameInput.text = "";
            if (passwordInput != null) passwordInput.text = "";
        }
    }

    private IEnumerator LoginCoroutine()
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(usernameInput.text) || string.IsNullOrWhiteSpace(passwordInput.text))
        {
            statusText.text = "Please enter username and password";
            yield break;
        }

        // Create form data
        Dictionary<string, string> formData = new Dictionary<string, string>
        {
            { "username", usernameInput.text },
            { "password", passwordInput.text }
        };

        // Create web request
        UnityWebRequest request = UnityWebRequest.Post(apiUrl, formData);
        request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

        // Send request
        statusText.text = "Logging in...";
        yield return request.SendWebRequest();

        // Get the response text regardless of success or failure
        string responseText = request.downloadHandler.text;

        // Log detailed response for debugging
        Debug.Log($"Login response: Code={request.responseCode}, Text={responseText}");

        try
        {
            // Try to parse the response regardless of HTTP status code
            if (!string.IsNullOrEmpty(responseText))
            {
                LoginResponse response = JsonUtility.FromJson<LoginResponse>(responseText);

                if (response != null)
                {
                    // Check response status
                    if (response.status == "success")
                    {
                        // Store token and key in SessionManager
                        if (SessionManager.Instance != null)
                        {
                            SessionManager.Instance.SetSessionData(response.token, response.key1, response.nonce, usernameInput.text);
                        }
                        else
                        {
                            // Fallback to static variables if SessionManager not available
                            JWT_Token = response.token;
                            Key1 = response.key1;
                            CurrentNonce = response.nonce;
                        }

                        statusText.text = "Login successful!";
                        Debug.Log("Login successful. Token: " + response.token);

                        // Navigate to the next scene in build settings after a short delay
                        // to allow the user to see the success message
                        StartCoroutine(LoadNextSceneAfterDelay(1.0f));
                    }
                    else
                    {
                        // Show the error message from the server
                        statusText.text = "Login failed: " + response.message;
                        Debug.LogError("Login failed: " + response.message);
                    }

                    // Early return if we successfully parsed the response
                    yield break;
                }
            }

            // If we get here, either responseText was empty or parsing failed
            // Now check for HTTP errors
            if (request.result != UnityWebRequest.Result.Success)
            {
                // For HTTP errors, we still try to extract message from JSON if possible
                if (!string.IsNullOrEmpty(responseText) && responseText.Contains("message"))
                {
                    try
                    {
                        // A simpler JSON parse attempt for just the message
                        var simpleParse = JsonUtility.FromJson<LoginResponse>(responseText);
                        if (simpleParse != null && !string.IsNullOrEmpty(simpleParse.message))
                        {
                            statusText.text = "Login failed: " + simpleParse.message;
                            Debug.LogError("Login error with message: " + simpleParse.message);
                            yield break;
                        }
                    }
                    catch
                    {
                        // If this also fails, fall back to the default error handling below
                    }
                }

                // If we couldn't parse a message, show the HTTP error
                Debug.LogError("Login Error: " + request.error);
                statusText.text = "Connection error: " + request.error;
            }
            else
            {
                // This is a fallback in case the response doesn't match our expected format
                statusText.text = "Unexpected server response";
                Debug.LogError("Unexpected server response format");
            }
        }
        catch (Exception ex)
        {
            statusText.text = "Error: " + (ex.Message.Length > 50 ? ex.Message.Substring(0, 50) + "..." : ex.Message);
            Debug.LogError("Error parsing login response: " + ex.Message);
        }
    }

    private IEnumerator LoadNextSceneAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);

        // Load the next scene in build settings
        int currentSceneIndex = SceneManager.GetActiveScene().buildIndex;
        SceneManager.LoadScene(currentSceneIndex + 1);
    }

    [Serializable]
    private class LoginResponse
    {
        public string status;
        public string message;
        public string token;
        public string key1;
        public string nonce;
        public int token_expires_in; // 24 hours in seconds
        public string token_type; // "Bearer"
    }
}