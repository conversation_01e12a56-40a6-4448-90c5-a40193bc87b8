-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Apr 19, 2025 at 07:28 PM
-- Server version: 11.4.5-MariaDB
-- PHP Version: 8.3.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `roshdso3_dbgame`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `cleanup_expired_key_parts` ()   BEGIN
    -- علامت‌گذاری کلیدهای تکراری به عنوان استفاده شده
    UPDATE `user_key_parts` AS ukp1
    JOIN (
        SELECT user_id, key_part, COUNT(*) as cnt
        FROM `user_key_parts`
        WHERE used = 0 AND expires_at > NOW()
        GROUP BY user_id, key_part
        HAVING COUNT(*) > 1
    ) AS ukp2 ON ukp1.user_id = ukp2.user_id AND ukp1.key_part = ukp2.key_part
    SET ukp1.used = 1
    LIMIT 100;
    
    -- حذف کلیدهای خالی
    DELETE FROM `user_key_parts` 
    WHERE key_part = '' OR key_part IS NULL;
    
    -- پاکسازی کلیدهای منقضی شده یا قدیمی استفاده شده
    DELETE FROM `user_key_parts` 
    WHERE (expires_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
       OR (used = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 3 DAY));
    
    -- حفظ حداکثر 5 کلید استفاده نشده برای هر کاربر
    DELETE ukp1 FROM `user_key_parts` ukp1
    LEFT JOIN (
        SELECT user_id, id
        FROM `user_key_parts`
        WHERE used = 0 AND expires_at > NOW()
        ORDER BY created_at DESC
    ) ukp2 ON ukp1.id = ukp2.id
    WHERE ukp1.used = 0 
      AND ukp2.id IS NULL
      AND ukp1.user_id IN (
        SELECT user_id 
        FROM (
            SELECT user_id, COUNT(*) as key_count
            FROM `user_key_parts`
            WHERE used = 0 AND expires_at > NOW()
            GROUP BY user_id
            HAVING COUNT(*) > 5
        ) as t
      );
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `cleanup_expired_tokens` ()   BEGIN
    -- حذف توکن‌های منقضی شده (با حاشیه ایمنی 1 ساعت)
    DELETE FROM `user_tokens` 
    WHERE expires_at < DATE_SUB(NOW(), INTERVAL 1 HOUR);
    
    -- محدود کردن تعداد توکن‌های هر کاربر (حفظ 5 توکن آخر)
    DELETE t1 FROM `user_tokens` t1
    LEFT JOIN (
        SELECT user_id, id
        FROM `user_tokens`
        ORDER BY created_at DESC
        LIMIT 1000000
    ) t2 ON t1.user_id = t2.user_id AND t1.id = t2.id
    INNER JOIN (
        SELECT user_id, COUNT(*) as token_count
        FROM `user_tokens`
        GROUP BY user_id
        HAVING token_count > 5
    ) t3 ON t1.user_id = t3.user_id
    WHERE t2.id IS NULL;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `fix_stuck_keys` ()   BEGIN
    -- پیدا کردن کاربرانی که تعداد زیادی کلید استفاده نشده دارند
    CREATE TEMPORARY TABLE IF NOT EXISTS users_with_many_keys AS
    SELECT user_id, COUNT(*) as key_count
    FROM `user_key_parts`
    WHERE used = 0 AND expires_at > NOW()
    GROUP BY user_id
    HAVING COUNT(*) > 10;
    
    -- ثبت در لاگ امنیتی
    INSERT INTO `security_log` (user_id, event_type, details)
    SELECT user_id, 'stuck_keys_detected', CONCAT('User has ', key_count, ' unused keys')
    FROM users_with_many_keys;
    
    -- علامت‌گذاری همه کلیدها به جز 2 کلید جدید
    UPDATE `user_key_parts` ukp1
    LEFT JOIN (
        SELECT id, user_id
        FROM `user_key_parts`
        WHERE used = 0 AND expires_at > NOW()
        ORDER BY created_at DESC
        LIMIT 2
    ) ukp2 ON ukp1.id = ukp2.id
    SET ukp1.used = 1
    WHERE ukp1.user_id IN (SELECT user_id FROM users_with_many_keys)
      AND ukp2.id IS NULL
      AND ukp1.used = 0;
    
    -- پاکسازی جدول موقت
    DROP TEMPORARY TABLE IF EXISTS users_with_many_keys;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `reset_user_keys` (IN `user_id_param` INT)   BEGIN
    -- علامت‌گذاری تمام کلیدهای موجود به عنوان استفاده شده
    UPDATE `user_key_parts` 
    SET used = 1
    WHERE user_id = user_id_param;
    
    -- ثبت در لاگ امنیتی
    INSERT INTO `security_log` (user_id, event_type, details)
    VALUES (user_id_param, 'manual_key_reset', 'All keys marked as used by admin');
    
    -- نمایش تعداد رکوردهای بروزرسانی شده
    SELECT CONCAT('Reset ', ROW_COUNT(), ' keys for user ID ', user_id_param) AS result;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `total_fee` decimal(10,2) DEFAULT 0.00 COMMENT 'مجموع کارمزد جمع‌آوری شده'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `email`, `role`, `status`, `last_login`, `created_at`, `updated_at`, `total_fee`) VALUES
(1, 'admin', '$2y$12$FhXRzLxM8omYGsGFt0QuOO.Em14eeNz8Qj.ypUw6FIHbNra0xDHWe', '<EMAIL>', 'super_admin', 'active', '2025-04-19 19:15:52', '2025-03-28 17:03:52', '2025-04-19 19:24:07', 66.00);

-- --------------------------------------------------------

--
-- Table structure for table `cars`
--

CREATE TABLE `cars` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `attributes` text DEFAULT NULL COMMENT 'ویژگی‌های خودرو به صورت JSON',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `crypto_transactions`
--

CREATE TABLE `crypto_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `transaction_type` enum('deposit','withdrawal') NOT NULL,
  `amount` decimal(18,8) NOT NULL,
  `wallet_address` varchar(42) NOT NULL,
  `tx_hash` varchar(66) DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','manual') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `game_stats`
--

CREATE TABLE `game_stats` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `games_count` int(11) NOT NULL DEFAULT 0,
  `wins_count` int(11) NOT NULL DEFAULT 0,
  `losses_count` int(11) NOT NULL DEFAULT 0,
  `cars_data` text DEFAULT NULL COMMENT 'کلیدهای خودروهای خریداری شده به فرمت key string (1,1);',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `game_stats`
--

INSERT INTO `game_stats` (`id`, `user_id`, `games_count`, `wins_count`, `losses_count`, `cars_data`, `updated_at`, `created_at`) VALUES
(28, 1, 10, 5, 5, NULL, '2025-04-10 19:22:46', '2025-04-04 23:17:55');

-- --------------------------------------------------------

--
-- Table structure for table `languages`
--

CREATE TABLE `languages` (
  `code` varchar(10) NOT NULL,
  `name` varchar(50) NOT NULL,
  `native_name` varchar(100) DEFAULT NULL,
  `flag_icon` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `text_direction` enum('ltr','rtl') NOT NULL DEFAULT 'ltr',
  `display_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `languages`
--

INSERT INTO `languages` (`code`, `name`, `native_name`, `flag_icon`, `is_active`, `is_default`, `text_direction`, `display_order`) VALUES
('ar', 'Arabic', 'العربية', 'flag-ar', 1, 0, 'rtl', 3),
('de', 'German', 'Deutsch', 'flag-de', 1, 0, 'ltr', 7),
('en', 'English', 'English', 'flag-en', 1, 1, 'ltr', 1),
('es', 'Spanish', 'Español', 'flag-es', 1, 0, 'ltr', 4),
('fa', 'Persian', 'فارسی', 'flag-fa', 1, 0, 'rtl', 11),
('hi', 'Hindi', 'हिन्दी', 'flag-hi', 1, 0, 'ltr', 10),
('ja', 'Japanese', '日本語', 'flag-ja', 1, 0, 'ltr', 8),
('pt', 'Portuguese', 'Português', 'flag-pt', 1, 0, 'ltr', 9),
('ru', 'Russian', 'Русский', 'flag-ru', 1, 0, 'ltr', 5),
('tr', 'Turkish', 'Türkçe', 'flag-tr', 1, 0, 'ltr', 6),
('zh', 'Chinese (Mandarin)', '中文 (普通话)', 'flag-zh', 1, 0, 'ltr', 2);

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_count` int(11) NOT NULL DEFAULT 1,
  `last_attempt` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `nonces`
--

CREATE TABLE `nonces` (
  `id` int(11) NOT NULL,
  `nonce` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `nonces`
--

INSERT INTO `nonces` (`id`, `nonce`, `user_id`, `created_at`) VALUES
(1382, 'bbcc2f69467c46095628ba3412e66c31', 20, '2025-04-14 21:03:01');

-- --------------------------------------------------------

--
-- Table structure for table `race_tokens`
--

CREATE TABLE `race_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(32) NOT NULL,
  `stake_amount` decimal(10,2) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `used` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rate_limits`
--

CREATE TABLE `rate_limits` (
  `user_id` int(11) NOT NULL,
  `request_count` int(11) DEFAULT 1,
  `last_request_time` datetime DEFAULT NULL,
  `blocked_until` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `referrals`
--

CREATE TABLE `referrals` (
  `id` int(11) NOT NULL,
  `referrer_id` int(11) NOT NULL,
  `referred_id` int(11) NOT NULL,
  `registration_rewarded` tinyint(1) DEFAULT 0,
  `race_rewarded` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `referrals`
--

INSERT INTO `referrals` (`id`, `referrer_id`, `referred_id`, `registration_rewarded`, `race_rewarded`, `created_at`) VALUES
(1, 18, 20, 1, 1, '2025-04-14 19:03:01');

-- --------------------------------------------------------

--
-- Table structure for table `referral_transactions`
--

CREATE TABLE `referral_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `fee` decimal(15,2) NOT NULL DEFAULT 0.00,
  `wallet_address` varchar(255) NOT NULL,
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
  `tx_hash` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `completed_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `referral_transactions`
--

INSERT INTO `referral_transactions` (`id`, `user_id`, `amount`, `fee`, `wallet_address`, `status`, `tx_hash`, `notes`, `created_at`, `completed_at`, `updated_at`) VALUES
(1, 18, 10.00, 0.00, '******************************************', 'pending', NULL, 'برداشت پاداش معرف', '2025-04-15 19:38:16', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `register_attempts`
--

CREATE TABLE `register_attempts` (
  `id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_count` int(11) NOT NULL DEFAULT 1,
  `last_attempt` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `register_attempts`
--

INSERT INTO `register_attempts` (`id`, `ip_address`, `attempt_count`, `last_attempt`) VALUES
(10, '************', 4, '2025-04-14 19:03:13');

-- --------------------------------------------------------

--
-- Table structure for table `security_log`
--

CREATE TABLE `security_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `security_log`
--

INSERT INTO `security_log` (`id`, `user_id`, `event_type`, `details`, `created_at`) VALUES
(1, 18, 'referral_fee', 'Referral fee share 0.5 USDT added to referrer ID 18 from withdrawal fee of user ID 20', '2025-04-14 21:46:29'),
(2, 18, 'referral_fee', 'Referral fee share 0.5 USDT added to referrer ID 18 from withdrawal fee of user ID 20', '2025-04-15 19:42:44'),
(3, 18, 'referral_fee', 'Referral fee share 0.5 USDT added to referrer ID 18 from withdrawal fee of user ID 20', '2025-04-15 21:13:02');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`, `updated_at`) VALUES
(1, 'withdrawal_fee', '2', 'کارمزد برداشت به درصد', '2025-03-29 18:02:53'),
(2, 'min_withdrawal', '10', 'حداقل مقدار قابل برداشت', '2025-03-28 17:03:52'),
(3, 'min_deposit', '10', 'حداقل مقدار قابل واریز', '2025-03-29 18:00:48'),
(4, 'usdt_contract', '******************************************', 'آدرس قرارداد USDT در شبکه BSC', '2025-04-17 22:06:10'),
(5, 'admin_wallet', '******************************************', 'آدرس کیف پول ادمین', '2025-04-17 22:05:45'),
(6, 'bsc_chain_id', '56', 'شناسه شبکه BSC', '2025-04-17 22:09:24'),
(7, 'bsc_rpc_url', 'https://bsc-dataseed.binance.org', 'آدرس RPC شبکه BSC', '2025-04-17 22:09:24'),
(8, 'usdt_decimals', '18', 'تعداد رقم اعشار توکن USDT', '2025-04-17 22:09:24'),
(9, 'auto_withdrawal', '0', 'پرداخت خودکار برداشت‌ها', '2025-03-29 18:02:53'),
(10, 'site_title', 'عنوان سایت', NULL, '2025-03-28 17:07:16'),
(11, 'site_description', ' توضیحات سایت ', NULL, '2025-03-28 17:07:16'),
(12, 'maintenance_mode', '0', NULL, '2025-04-18 16:36:10'),
(13, 'maintenance_message', 'در حالت تعمیرات، فقط مدیران و صفحات خاص قابل دسترسی خواهند بود.', NULL, '2025-04-18 11:48:37'),
(14, 'admin_email', '<EMAIL>', NULL, '2025-03-28 17:08:03'),
(15, 'notification_email', '<EMAIL>', NULL, '2025-03-28 17:08:03'),
(19, 'enable_referral_fee', '1', 'فعال یا غیرفعال بودن اختصاص بخشی از کارمزد برداشت به معرف', '2025-04-15 19:42:33'),
(20, 'referral_fee_share', '0.5', 'مقدار کارمزدی که به معرف کاربر اختصاص می‌یابد', '2025-04-14 21:36:56');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','withdraw') NOT NULL,
  `amount` decimal(18,8) NOT NULL,
  `tx_hash` varchar(255) DEFAULT NULL,
  `wallet_address` varchar(255) DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','manual') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `fee` decimal(10,2) DEFAULT 0.00 COMMENT 'مبلغ کارمزد برداشت'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`id`, `user_id`, `type`, `amount`, `tx_hash`, `wallet_address`, `status`, `notes`, `created_at`, `updated_at`, `fee`) VALUES
(44, 1, 'deposit', 20.00000000, '0x39f0892368d3dfeec29579cc50eef9419fb5aa09129b9eb1097bd5e553c8e71', '******************************************', 'pending', 'تراکنش تایید شد (با 25 تاییدیه BSC) - BSCScan: https://bscscan.com/tx/0x39f0892368d3dfeec29579cc50eef9419fb5aa09129b9eb1097bd5e553c8e710', '2025-04-17 22:43:19', '2025-04-17 22:47:59', 0.00),
(45, 1, 'deposit', 30.00000000, '0x7a03ec3374035e75a84204a35abe5e025c112686783077e36e67c5eec6d53ccf', '******************************************', 'completed', 'تراکنش تایید شد (با 13 تاییدیه BSC) - BSCScan: https://bscscan.com/tx/0x7a03ec3374035e75a84204a35abe5e025c112686783077e36e67c5eec6d53ccf', '2025-04-17 22:43:55', '2025-04-17 22:44:39', 0.00),
(46, 1, 'deposit', 32.00000000, '0x54f26ae1ee8e31ce01618876cf83a899c7333afb30f704c84fb6b9ef270cf5ce', '******************************************', 'completed', 'تراکنش تایید شد (با 31 تاییدیه BSC) - BSCScan: https://bscscan.com/tx/0x54f26ae1ee8e31ce01618876cf83a899c7333afb30f704c84fb6b9ef270cf5ce', '2025-04-17 22:50:18', '2025-04-17 22:51:55', 0.00),
(47, 1, 'deposit', 20.00000000, '0xaf14199c75b7e1e642762a4d0adca163658ceb1f70c24bbd6a21a73c6e4dd3fb', '******************************************', 'pending', 'BSCScan: https://bscscan.com/tx/0xaf14199c75b7e1e642762a4d0adca163658ceb1f70c24bbd6a21a73c6e4dd3fb', '2025-04-18 01:02:13', '2025-04-18 01:02:13', 0.00),
(50, 1, 'withdrawal', 20.00000000, NULL, '******************************************', 'pending', NULL, '2025-04-19 19:23:00', '2025-04-19 19:23:00', 2.00),
(51, 1, 'withdrawal', 23.00000000, NULL, '******************************************', 'pending', NULL, '2025-04-19 19:23:11', '2025-04-19 19:23:11', 2.00),
(52, 18, 'withdrawal', 8.00000000, NULL, '******************************************', 'pending', NULL, '2025-04-19 19:23:57', '2025-04-19 19:23:57', 2.00),
(53, 18, 'withdrawal', 9.00000000, NULL, '******************************************', 'pending', NULL, '2025-04-19 19:24:07', '2025-04-19 19:24:07', 2.00);

-- --------------------------------------------------------

--
-- Table structure for table `translations`
--

CREATE TABLE `translations` (
  `id` int(11) NOT NULL,
  `source_text` text NOT NULL,
  `source_lang` varchar(10) NOT NULL,
  `target_lang` varchar(10) NOT NULL,
  `translated_text` text NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `translations`
--

INSERT INTO `translations` (`id`, `source_text`, `source_lang`, `target_lang`, `translated_text`, `created_at`) VALUES
(1, 'Game Site', 'en', 'es', 'Sitio de juego', '2025-04-18 16:43:41'),
(2, 'Home', 'en', 'es', 'Hogar', '2025-04-18 16:43:41'),
(3, 'Login', 'en', 'es', 'Acceso', '2025-04-18 16:43:41'),
(4, 'Register', 'en', 'es', 'Registro', '2025-04-18 16:43:41'),
(5, 'Game Site', 'en', 'tr', 'Oyun sitesi', '2025-04-18 16:43:49'),
(6, 'Home', 'en', 'tr', 'Ev', '2025-04-18 16:43:49'),
(7, 'Game Site', 'en', 'de', 'Spiele', '2025-04-18 16:44:43'),
(8, 'Home', 'en', 'de', 'Heim', '2025-04-18 16:44:43'),
(9, 'Login', 'en', 'de', 'Login', '2025-04-18 16:44:43'),
(10, 'Register', 'en', 'de', 'Registrieren', '2025-04-18 16:44:43'),
(11, 'Game Site', 'en', 'pt', 'Site de jogo', '2025-04-18 16:44:59'),
(12, 'Home', 'en', 'pt', 'Lar', '2025-04-18 16:44:59'),
(13, 'Login', 'en', 'pt', 'Conecte-se', '2025-04-18 16:44:59'),
(14, 'Register', 'en', 'pt', 'Registrar', '2025-04-18 16:44:59');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `wallet_address` varchar(42) DEFAULT NULL,
  `balance` decimal(18,8) DEFAULT 0.00000000,
  `win_balance` decimal(18,8) DEFAULT 0.00000000,
  `status` enum('active','inactive','banned') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `referral_code` varchar(10) DEFAULT NULL,
  `referral_balance` decimal(10,2) DEFAULT 0.00,
  `referral_win` decimal(10,2) DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `wallet_address`, `balance`, `win_balance`, `status`, `last_login`, `created_at`, `updated_at`, `referral_code`, `referral_balance`, `referral_win`) VALUES
(1, 'aaa', '$2y$12$RbSTalc5Ht6bnlABW/9JX.7P3nqr7DxjSfudFHI2nMhatrhJFd/tm', '<EMAIL>', '******************************************', 527.00000000, 531.00000000, 'active', '2025-04-19 16:14:19', '2025-03-28 16:04:45', '2025-04-19 19:23:11', NULL, 0.00, 500.00),
(18, 'erere', '$2y$12$LG5DIZZgWj5d2LlvuaA9deQFc/UCiMmyh5nzFsC0OR5BbyPN0gGXe', '<EMAIL>', '******************************************', 503.00000000, 0.00000000, 'active', '2025-04-19 17:23:25', '2025-04-12 21:35:01', '2025-04-19 19:24:07', 'L6QO7H9R', 920.50, 31.00),
(20, 'www', '$2y$12$L.5mjEarhNYxTBvFz9IlJe1Dr7S4kBHgGwEyaQdFFK0hHzDCXIIDm', '<EMAIL>', '******************************************', 605.00000000, 419.00000000, 'active', '2025-04-16 20:35:52', '2025-04-14 21:03:01', '2025-04-16 22:35:52', 'NCKR4IAW', 0.00, 6.00);

-- --------------------------------------------------------

--
-- Table structure for table `user_cars`
--

CREATE TABLE `user_cars` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `car_id` varchar(20) NOT NULL,
  `purchase_date` datetime DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 0,
  `last_activated` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `user_cars`
--

INSERT INTO `user_cars` (`id`, `user_id`, `car_id`, `purchase_date`, `is_active`, `last_activated`) VALUES
(8, 18, 'Car:0', '2025-04-13 22:57:31', 0, NULL),
(9, 18, 'Car:1', '2025-04-13 22:57:47', 0, NULL),
(10, 18, 'Car:2', '2025-04-13 22:58:08', 0, NULL),
(11, 18, 'Car:3', '2025-04-13 23:00:07', 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_jwt_secrets`
--

CREATE TABLE `user_jwt_secrets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `jwt_secret` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_jwt_secrets`
--

INSERT INTO `user_jwt_secrets` (`id`, `user_id`, `jwt_secret`, `created_at`) VALUES
(56, 1, '468ef2d19ca59508837a0566708fcaff7ff5689e08731d84e0ab9d5b133b3375', '2025-04-11 07:15:59'),
(144, 18, '1e51e21da8f75bc9b93dde45fccb33a660636aecdc3bc3b108be416533b12939', '2025-04-12 19:35:01'),
(179, 20, '8eca9f1d4dc2e684033c018e376a2136992a2a54fd9de4ddb737651775d3e4fd', '2025-04-14 19:03:01');

-- --------------------------------------------------------

--
-- Table structure for table `user_key2`
--

CREATE TABLE `user_key2` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `key_value` varchar(32) NOT NULL,
  `session_id` varchar(32) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_used` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_key_parts`
--

CREATE TABLE `user_key_parts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `key_part` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `used` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_key_parts`
--

INSERT INTO `user_key_parts` (`id`, `user_id`, `key_part`, `created_at`, `expires_at`, `used`) VALUES
(963, 20, '709140254b951a5451bdef1d4659f6e9', '2025-04-14 21:03:01', '2025-04-15 22:33:01', 0);

-- --------------------------------------------------------

--
-- Table structure for table `user_tokens`
--

CREATE TABLE `user_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `last_activity` datetime NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_tokens`
--

INSERT INTO `user_tokens` (`id`, `user_id`, `token`, `expires_at`, `last_activity`, `created_at`, `updated_at`) VALUES
(858, 20, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyMCwiZXhwIjoxNzQ0NjYwOTgxLCJpYXQiOjE3NDQ2NTczODEsImp0aSI6IjA0OWQ4MjRhNWM1NmMwNzEifQ.6H6eLg0WHo8EpEd-9U80og6NgtNpwEXKMQO2QpcHcKQ', '2025-04-14 22:48:01', '2025-04-14 21:03:01', '2025-04-14 21:03:01', '2025-04-14 21:03:01');

-- --------------------------------------------------------

--
-- Table structure for table `wallet_transactions`
--

CREATE TABLE `wallet_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(18,8) NOT NULL,
  `type` enum('credit','debit') NOT NULL,
  `description` text DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `balance_before` decimal(18,8) DEFAULT NULL,
  `balance_after` decimal(18,8) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `cars`
--
ALTER TABLE `cars`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crypto_transactions`
--
ALTER TABLE `crypto_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `status` (`status`),
  ADD KEY `transaction_type` (`transaction_type`),
  ADD KEY `tx_hash` (`tx_hash`);

--
-- Indexes for table `game_stats`
--
ALTER TABLE `game_stats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`code`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ip_address` (`ip_address`);

--
-- Indexes for table `nonces`
--
ALTER TABLE `nonces`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nonce` (`nonce`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `created_at` (`created_at`),
  ADD KEY `idx_nonces_created_at` (`created_at`);

--
-- Indexes for table `race_tokens`
--
ALTER TABLE `race_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`,`token`);

--
-- Indexes for table `rate_limits`
--
ALTER TABLE `rate_limits`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `referrals`
--
ALTER TABLE `referrals`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `referred_id` (`referred_id`),
  ADD KEY `referrer_id` (`referrer_id`),
  ADD KEY `idx_referrals_referred_id` (`referred_id`);

--
-- Indexes for table `referral_transactions`
--
ALTER TABLE `referral_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `register_attempts`
--
ALTER TABLE `register_attempts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ip_address` (`ip_address`);

--
-- Indexes for table `security_log`
--
ALTER TABLE `security_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `event_type` (`event_type`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `status` (`status`),
  ADD KEY `tx_hash` (`tx_hash`);

--
-- Indexes for table `translations`
--
ALTER TABLE `translations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `text_lang_pair` (`source_text`(255),`source_lang`,`target_lang`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `referral_code` (`referral_code`);

--
-- Indexes for table `user_cars`
--
ALTER TABLE `user_cars`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`,`car_id`),
  ADD UNIQUE KEY `unq_user_car` (`user_id`,`car_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_car_id` (`car_id`);

--
-- Indexes for table `user_jwt_secrets`
--
ALTER TABLE `user_jwt_secrets`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `idx_user_jwt_created` (`created_at`);

--
-- Indexes for table `user_key2`
--
ALTER TABLE `user_key2`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_key` (`user_id`,`key_value`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_key_value` (`key_value`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_user_session` (`user_id`,`session_id`);

--
-- Indexes for table `user_key_parts`
--
ALTER TABLE `user_key_parts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `expires_at` (`expires_at`),
  ADD KEY `idx_key_parts_expires_at` (`expires_at`);

--
-- Indexes for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `expires_at` (`expires_at`);

--
-- Indexes for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `type` (`type`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `cars`
--
ALTER TABLE `cars`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `crypto_transactions`
--
ALTER TABLE `crypto_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `game_stats`
--
ALTER TABLE `game_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `nonces`
--
ALTER TABLE `nonces`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1383;

--
-- AUTO_INCREMENT for table `race_tokens`
--
ALTER TABLE `race_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `referrals`
--
ALTER TABLE `referrals`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `referral_transactions`
--
ALTER TABLE `referral_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `register_attempts`
--
ALTER TABLE `register_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `security_log`
--
ALTER TABLE `security_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `translations`
--
ALTER TABLE `translations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `user_cars`
--
ALTER TABLE `user_cars`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `user_jwt_secrets`
--
ALTER TABLE `user_jwt_secrets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=180;

--
-- AUTO_INCREMENT for table `user_key2`
--
ALTER TABLE `user_key2`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `user_key_parts`
--
ALTER TABLE `user_key_parts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=964;

--
-- AUTO_INCREMENT for table `user_tokens`
--
ALTER TABLE `user_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=859;

--
-- AUTO_INCREMENT for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `crypto_transactions`
--
ALTER TABLE `crypto_transactions`
  ADD CONSTRAINT `crypto_transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `game_stats`
--
ALTER TABLE `game_stats`
  ADD CONSTRAINT `game_stats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `game_stats_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `nonces`
--
ALTER TABLE `nonces`
  ADD CONSTRAINT `nonces_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `referrals`
--
ALTER TABLE `referrals`
  ADD CONSTRAINT `referrals_ibfk_1` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `referrals_ibfk_2` FOREIGN KEY (`referred_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `security_log`
--
ALTER TABLE `security_log`
  ADD CONSTRAINT `security_log_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_jwt_secrets`
--
ALTER TABLE `user_jwt_secrets`
  ADD CONSTRAINT `user_jwt_secrets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_key2`
--
ALTER TABLE `user_key2`
  ADD CONSTRAINT `user_key2_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_key_parts`
--
ALTER TABLE `user_key_parts`
  ADD CONSTRAINT `user_key_parts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD CONSTRAINT `user_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD CONSTRAINT `wallet_transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

DELIMITER $$
--
-- Events
--
CREATE DEFINER=`roshdso3`@`localhost` EVENT `hourly_key_parts_cleanup` ON SCHEDULE EVERY 1 HOUR STARTS '2025-04-03 18:22:51' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
    CALL cleanup_expired_key_parts();
END$$

CREATE DEFINER=`roshdso3`@`localhost` EVENT `daily_tokens_cleanup` ON SCHEDULE EVERY 1 DAY STARTS '2025-04-03 18:22:51' ON COMPLETION PRESERVE ENABLE DO BEGIN
    CALL cleanup_expired_tokens();
END$$

CREATE DEFINER=`roshdso3`@`localhost` EVENT `fix_stuck_keys_event` ON SCHEDULE EVERY 2 HOUR STARTS '2025-04-03 18:22:51' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
    CALL fix_stuck_keys();
END$$

DELIMITER ;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
