<?php
// Password recovery system
header('Content-Type: application/json');

// Include database connection
require_once 'inc/db.php';

// Function to generate a secure random token
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Function to send email
function sendPasswordResetEmail($email, $username, $token) {
    $resetLink = "https://game-gofaster.com/gam/api/reset_password.php?token=" . $token;

    // Use a clear, specific subject line that doesn't look like spam
    $subject = "Your Password Reset Link for Game-GoFaster";

    // Create a simple, clean email that's less likely to trigger spam filters
    $message = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='utf-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Password Reset</title>
    </head>
    <body style='margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.6; color: #333333;'>
        <table cellpadding='0' cellspacing='0' border='0' width='100%' style='background-color: #f5f5f5; padding: 20px;'>
            <tr>
                <td align='center'>
                    <table cellpadding='0' cellspacing='0' border='0' width='600' style='background-color: #ffffff; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);'>
                        <tr>
                            <td style='padding: 30px 30px 20px 30px;'>
                                <h1 style='margin: 0; font-size: 22px; color: #333333; font-weight: 600;'>Password Reset</h1>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding: 0 30px 20px 30px;'>
                                <p>Dear $username,</p>
                                <p>We received a request to reset your password. If you made this request, please use the link below to set a new password:</p>
                                <p style='text-align: center; margin: 30px 0;'>
                                    <a href='$resetLink' style='background-color: #4285f4; color: white; text-decoration: none; padding: 12px 25px; border-radius: 4px; font-weight: bold; display: inline-block;'>Reset Password</a>
                                </p>
                                <p>This link will expire in 3 hours for security reasons.</p>
                                <p>If you didn't request a password reset, you can safely ignore this email.</p>
                                <p>For security, this link can only be used once.</p>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding: 20px 30px 30px 30px; border-top: 1px solid #eeeeee;'>
                                <p style='margin: 0; color: #777777; font-size: 13px;'>
                                    Regards,<br>
                                    The Game Go Faster support team
                                </p>
                            </td>
                        </tr>
                    </table>
                    <table cellpadding='0' cellspacing='0' border='0' width='600'>
                        <tr>
                            <td style='padding: 15px 0; text-align: center; font-size: 12px; color: #777777;'>
                                <p>If the button above doesn't work, copy and paste this URL into your browser:</p>
                                <p style='word-break: break-all; font-size: 11px;'>$resetLink</p>
                                <p>&copy; " . date('Y') . " Game-GoFaster. All rights reserved.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    ";

    // Set up email headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type: text/html; charset=UTF-8" . "\r\n";
    $headers .= "From: Game-GoFaster Support <<EMAIL>>" . "\r\n";
    $headers .= "Reply-To: <EMAIL>" . "\r\n";

    // Try to send using PHP mail()
    $mail_sent = mail($email, $subject, $message, $headers);

    if ($mail_sent) {
        error_log("Password reset email sent to $email");
        return true;
    } else {
        error_log("Failed to send password reset email to $email");
        return false;
    }
}

// Main process
try {
    // Check if email is provided
    if (!isset($_POST['email']) || empty($_POST['email'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Please enter your email'
        ]);
        exit;
    }

    $email = trim($_POST['email']);

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid email format'
        ]);
        exit;
    }

    // Check if email exists in the database
    $stmt = $conn->prepare("SELECT id, username FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Email not found in our system'
        ]);
        exit;
    }

    $user = $result->fetch_assoc();
    $user_id = $user['id'];
    $username = $user['username'];

    // Generate a secure token
    $token = generateSecureToken();

    // Calculate expiration time (3 hours from now)
    $expiration = date('Y-m-d H:i:s', strtotime('+3 hours'));

    // Delete any existing reset tokens for this user
    $stmt = $conn->prepare("DELETE FROM password_reset_tokens WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();

    // Store the token in the database
    $stmt = $conn->prepare("INSERT INTO password_reset_tokens (user_id, token, expiration) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $token, $expiration);
    $stmt->execute();

    // Send the password reset email
    $emailSent = sendPasswordResetEmail($email, $username, $token);

    if ($emailSent) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Password reset link has been sent to your email'
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Error sending email. Please try again later'
        ]);
    }

} catch (Exception $e) {
    // Log the error (in a production environment)
    error_log("Password recovery error: " . $e->getMessage());

    // Return a generic error message to the client
    echo json_encode([
        'status' => 'error',
        'message' => 'A system error occurred. Please try again later'
    ]);
}

// Close the database connection
$conn->close();
?>
