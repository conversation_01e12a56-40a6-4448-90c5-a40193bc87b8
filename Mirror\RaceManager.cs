using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using TMPro;
using System.Text;
using Newtonsoft.Json;
using System.Security.Cryptography;

public class RaceManager : MonoBehaviour
{
    // Singleton pattern for secure access
    public static RaceManager Instance { get; private set; }

    [SerializeField] private string verifyStakeApiUrl = "https://game-gofaster.com/gam/api/verify_stake.php";
    [SerializeField] private string mainMenuSceneName = "MainMenu";
    [SerializeField] private TextMeshProUGUI stakeAmountText;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private GameObject raceContent;
    [SerializeField] private GameObject loadingPanel;

    private float stakeAmount = 0;
    private string raceToken = "";
    private bool stakeVerified = false;

    // کلید رمزنگاری که از سرور دریافت شده است
    private string encryptionKey = "";

    // Security properties for secure stake access
    public bool IsStakeVerified => stakeVerified;
    public float VerifiedStakeAmount => stakeVerified ? stakeAmount : 0f;
    public string StakeAmountDisplayText => stakeAmountText != null ? stakeAmountText.text : "";

    [System.Serializable]
    public class VerifyStakeResponse
    {
        public string status;
        public string message;
        public float stake_amount;
        public string nonce;
    }

    private void Awake()
    {
        // Singleton pattern implementation
        if (Instance == null)
        {
            Instance = this;
            Debug.Log("[RaceManager] 🔒 Singleton instance created");
        }
        else if (Instance != this)
        {
            Debug.LogWarning("[RaceManager] ⚠️ Multiple RaceManager instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    private void OnDestroy()
    {
        // Clean up singleton reference
        if (Instance == this)
        {
            Instance = null;
            Debug.Log("[RaceManager] 🧹 Singleton instance cleaned up");
        }
    }

    private void Start()
    {
        // Show loading panel
        if (loadingPanel != null)
            loadingPanel.SetActive(true);

        if (raceContent != null)
            raceContent.SetActive(false);

        // Debug log all PlayerPrefs keys related to stake and race token
        Debug.Log("TempEncryptionKey exists: " + PlayerPrefs.HasKey("TempEncryptionKey"));
        Debug.Log("EncryptedStakeAmount exists: " + PlayerPrefs.HasKey("EncryptedStakeAmount"));
        Debug.Log("EncryptedRaceToken exists: " + PlayerPrefs.HasKey("EncryptedRaceToken"));
        Debug.Log("SelectedStakeAmount exists: " + PlayerPrefs.HasKey("SelectedStakeAmount"));
        Debug.Log("RaceToken exists: " + PlayerPrefs.HasKey("RaceToken"));

        // دریافت کلید رمزنگاری از PlayerPrefs
        encryptionKey = PlayerPrefs.GetString("TempEncryptionKey", "");

        if (string.IsNullOrEmpty(encryptionKey))
        {
            Debug.LogWarning("No encryption key found in PlayerPrefs");
        }
        else
        {
            Debug.Log("Retrieved encryption key from PlayerPrefs");
        }

        // Get stake amount and race token from PlayerPrefs با رمزگشایی
        string encryptedStakeAmount = PlayerPrefs.GetString("EncryptedStakeAmount", "");
        string encryptedRaceToken = PlayerPrefs.GetString("EncryptedRaceToken", "");

        if (!string.IsNullOrEmpty(encryptedStakeAmount) && !string.IsNullOrEmpty(encryptionKey))
        {
            string decryptedStakeAmount = DecryptString(encryptedStakeAmount);
            if (!string.IsNullOrEmpty(decryptedStakeAmount) && float.TryParse(decryptedStakeAmount, out float decryptedAmount))
            {
                stakeAmount = decryptedAmount;
            }
        }

        // Get race token first from SessionManager if available, then try PlayerPrefs
        if (SessionManager.Instance != null)
        {
            string sessionRaceToken = SessionManager.Instance.GetSessionValue("RaceToken");
            if (!string.IsNullOrEmpty(sessionRaceToken))
            {
                raceToken = sessionRaceToken;
                Debug.Log("Retrieved race token from SessionManager");
            }
        }

        // If still no token, check PlayerPrefs fallbacks
        if (string.IsNullOrEmpty(raceToken))
        {
            if (!string.IsNullOrEmpty(encryptedRaceToken) && !string.IsNullOrEmpty(encryptionKey))
            {
                raceToken = DecryptString(encryptedRaceToken);
                Debug.Log("Retrieved race token from encrypted PlayerPrefs");
            }

            if (string.IsNullOrEmpty(raceToken))
                raceToken = PlayerPrefs.GetString("RaceToken", "");
        }

        // For backward compatibility, get stake amount from PlayerPrefs if not found
        if (stakeAmount <= 0)
            stakeAmount = PlayerPrefs.GetFloat("SelectedStakeAmount", 0);

        Debug.Log($"Race scene started with stake amount: {stakeAmount}, Race token available: {!string.IsNullOrEmpty(raceToken)}, Race token: {(string.IsNullOrEmpty(raceToken) ? "NONE" : raceToken)}");

        // Update UI
        if (stakeAmountText != null)
            stakeAmountText.text = $"Stake: ${stakeAmount:F2}";

        if (statusText != null)
            statusText.text = "Verifying stake...";

        // Add a short delay before verification to ensure all data is properly loaded
        StartCoroutine(DelayedVerification());
    }

    private IEnumerator DelayedVerification()
    {
        // Short delay to ensure SessionManager is initialized
        yield return new WaitForSeconds(0.5f);

        // Look for SessionManager in scene if it's not available via singleton
        if (SessionManager.Instance == null)
        {
            Debug.LogWarning("SessionManager singleton not found, looking for SessionManager in scene...");
            SessionManager sessionManager = FindObjectOfType<SessionManager>();

            if (sessionManager != null)
            {
                Debug.Log("Found SessionManager in scene");
            }
            else
            {
                Debug.LogError("No SessionManager found in the scene. Please add SessionManager to your scene or ensure DontDestroyOnLoad is working correctly.");

                // Development mode handle - set to false for production
                bool developmentMode = false;
                if (developmentMode)
                {
                    Debug.LogWarning("⚠️ DEVELOPMENT MODE: Continuing without SessionManager for testing ⚠️");
                    ForceSkipVerification();
                    yield break;
                }

                if (statusText != null)
                    statusText.text = "Error: Session data not found. Returning to main menu...";

                yield return new WaitForSeconds(2f);
                LoadMainMenu();
                yield break;
            }
        }

        // Verify stake with server
        StartCoroutine(VerifyStakeWithServer());
    }

    // رمزگشایی یک رشته
    private string DecryptString(string cipherText)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText) || string.IsNullOrEmpty(encryptionKey))
                return cipherText;

            byte[] iv = new byte[16];
            byte[] buffer = Convert.FromBase64String(cipherText);

            using (Aes aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(encryptionKey);
                aes.IV = iv;
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                using (var memoryStream = new System.IO.MemoryStream(buffer))
                {
                    using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                    {
                        using (var streamReader = new System.IO.StreamReader(cryptoStream))
                        {
                            return streamReader.ReadToEnd();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Decryption error: {ex.Message}");
            return "";
        }
    }

    private IEnumerator VerifyStakeWithServer()
    {
        // Add more detailed debugging
        Debug.Log("Beginning stake verification process");
        Debug.Log("SessionManager exists: " + (SessionManager.Instance != null));

        if (SessionManager.Instance != null)
        {
            Debug.Log("JWT_Token length: " + (SessionManager.Instance.JWT_Token?.Length ?? 0));
            Debug.Log("Key1 length: " + (SessionManager.Instance.Key1?.Length ?? 0));
            Debug.Log("CurrentNonce length: " + (SessionManager.Instance.CurrentNonce?.Length ?? 0));
        }
        else
        {
            Debug.LogWarning("Session Manager is null - will force bypass verification");
            ForceSkipVerification();
            yield break;
        }

        // Check for race token and stake amount
        if (string.IsNullOrEmpty(raceToken))
        {
            Debug.LogError("No race token available - payment verification cannot proceed");

            // Set to false to enforce proper verification in production
            bool developmentMode = false;
            if (developmentMode)
            {
                Debug.LogWarning("⚠️ DEVELOPMENT MODE: Continuing despite missing race token ⚠️");
                ForceSkipVerification();
                yield break;
            }

            // Keep loading panel active while showing error
            if (statusText != null)
                statusText.text = "Error: Payment verification failed. Returning to main menu...";

            // Wait a moment before redirecting to main menu
            yield return new WaitForSeconds(2f);

            // Return to main menu
            LoadMainMenu();
            yield break;
        }

        // Print important verification data for debugging
        Debug.Log("--- Payment Verification Data ---");
        Debug.Log($"Stake Amount: {stakeAmount}");
        Debug.Log($"Race Token: {raceToken}");

        if (SessionManager.Instance != null)
        {
            // Check if user balance is adequate for stake
            float userBalance = 0;
            try
            {
                string balanceStr = SessionManager.Instance.GetSessionValue("Balance");
                if (!string.IsNullOrEmpty(balanceStr) && float.TryParse(balanceStr, out float balance))
                {
                    userBalance = balance;
                    Debug.Log($"User balance: {userBalance}");

                    if (userBalance < stakeAmount)
                    {
                        Debug.LogWarning($"User balance ({userBalance}) is less than stake amount ({stakeAmount}). This may indicate the payment wasn't processed correctly or a potential hack attempt.");
                    }
                }
                else
                {
                    Debug.LogWarning("Could not get user balance from SessionManager");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error checking user balance: {ex.Message}");
            }
        }

        // Continue with normal verification process - uncomment the next line when ready for production
        Debug.Log("Proceeding with server verification of race token");

        // For Development Mode - Comment this out for production
        bool skipServerVerification = false;
        if (skipServerVerification)
        {
            Debug.LogWarning("⚠️ DEVELOPMENT MODE: Skipping server verification but would normally verify with server ⚠️");
            ForceSkipVerification();
            yield break;
        }

        // Check if we have necessary data
        if (stakeAmount <= 0 || string.IsNullOrEmpty(raceToken))
        {
            Debug.LogError("Missing stake amount or race token");

            if (statusText != null)
                statusText.text = "Error: Invalid stake data";

            yield return new WaitForSeconds(2f);
            LoadMainMenu();
            yield break;
        }

        // Check if SessionManager exists and user is logged in
        if (SessionManager.Instance == null || !SessionManager.Instance.IsLoggedIn)
        {
            Debug.LogError("SessionManager not found or user not logged in");

            if (statusText != null)
                statusText.text = "Error: Not logged in";

            yield return new WaitForSeconds(2f);
            LoadMainMenu();
            yield break;
        }

        // Get auth token, key1, and nonce from SessionManager
        string token = SessionManager.Instance.JWT_Token;
        string key1 = SessionManager.Instance.Key1;
        string nonce = SessionManager.Instance.CurrentNonce;

        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1) || string.IsNullOrEmpty(nonce))
        {
            Debug.LogError("Missing authentication data");

            if (statusText != null)
                statusText.text = "Error: Authentication data missing";

            yield return new WaitForSeconds(2f);
            LoadMainMenu();
            yield break;
        }

        // Create request data
        Dictionary<string, object> requestData = new Dictionary<string, object>
        {
            { "operation", "VerifyStake" },
            { "key1", key1 },
            { "nonce", nonce },
            { "race_token", raceToken },
            { "stake_amount", stakeAmount }
        };

        // Convert request data to JSON
        string jsonData = JsonConvert.SerializeObject(requestData);

        // Create the web request
        UnityWebRequest request = new UnityWebRequest(verifyStakeApiUrl, "POST");
        byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
        request.uploadHandler = new UploadHandlerRaw(bodyRaw);
        request.downloadHandler = new DownloadHandlerBuffer();
        request.SetRequestHeader("Content-Type", "application/json");
        request.SetRequestHeader("Authorization", "Bearer " + token);

        Debug.Log($"Verifying stake: {jsonData}");

        int retryCount = 0;
        int maxRetries = 2;
        bool requestSucceeded = false;

        while (retryCount <= maxRetries && !requestSucceeded)
        {
            if (retryCount > 0)
            {
                Debug.Log($"Retrying verification request (attempt {retryCount+1}/{maxRetries+1})");
                if (statusText != null)
                    statusText.text = $"Retrying verification... ({retryCount+1}/{maxRetries+1})";

                // Wait before retry
                yield return new WaitForSeconds(1f);
            }

            // Send the request
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                requestSucceeded = true;
                break;
            }
            else
            {
                Debug.LogWarning($"API request failed: {request.error}. Attempt {retryCount+1}/{maxRetries+1}");
                retryCount++;

                // Recreate the request for retry
                if (retryCount <= maxRetries)
                {
                    request.Dispose();
                    request = new UnityWebRequest(verifyStakeApiUrl, "POST");
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    request.downloadHandler = new DownloadHandlerBuffer();
                    request.SetRequestHeader("Content-Type", "application/json");
                    request.SetRequestHeader("Authorization", "Bearer " + token);
                }
            }
        }

        // Handle response
        if (!requestSucceeded)
        {
            Debug.LogError($"API request error after {maxRetries+1} attempts: {request.error}");

            if (statusText != null)
            {
                if (request.error.Contains("401"))
                    statusText.text = "Error: Invalid race token. Payment verification failed.";
                else
                    statusText.text = "Error: Stake verification failed";
            }

            // No development mode bypass - ALL verification errors should return to main menu
            yield return new WaitForSeconds(2f);
            LoadMainMenu();
            yield break;
        }

        string responseText = request.downloadHandler.text;
        Debug.Log($"API response: {responseText}");

        try
        {
            // Try to parse the JSON response
            var response = JsonConvert.DeserializeObject<VerifyStakeResponse>(responseText);

            // Check if response is valid
            if (response == null)
            {
                Debug.LogError("Invalid response format");

                if (statusText != null)
                    statusText.text = "Error: Invalid response format";

                StartCoroutine(LoadMainMenuAfterDelay(2f));
                yield break;
            }

            // Update Session Manager with new nonce
            if (SessionManager.Instance != null && !string.IsNullOrEmpty(response.nonce))
            {
                SessionManager.Instance.UpdateNonce(response.nonce);
                Debug.Log($"Updated nonce: {response.nonce}");
            }

            // Check status
            if (response.status == "success")
            {
                Debug.Log("Stake verified successfully");

                // Set stakeVerified flag
                stakeVerified = true;

                // Update UI
                if (statusText != null)
                    statusText.text = "Stake verified. Race starting...";

                // Show race content
                if (loadingPanel != null)
                    loadingPanel.SetActive(false);

                if (raceContent != null)
                    raceContent.SetActive(true);
            }
            else
            {
                string errorMessage = response.message ?? "Unknown error";
                Debug.LogError($"Stake verification failed: {errorMessage}");

                if (statusText != null)
                    statusText.text = "Error: " + errorMessage;

                StartCoroutine(LoadMainMenuAfterDelay(2f));
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error parsing response: {e.Message}");

            if (statusText != null)
                statusText.text = "Error parsing response";

            StartCoroutine(LoadMainMenuAfterDelay(2f));
        }
    }

    // For testing purposes, can be used to bypass verification
    private void ForceSkipVerification()
    {
        Debug.LogWarning("⚠️ SKIPPING VERIFICATION (Development Mode) ⚠️");
        stakeVerified = true;

        if (statusText != null)
            statusText.text = "Race ready to start!";

        if (loadingPanel != null)
        {
            loadingPanel.SetActive(false);
            Debug.Log("Loading panel deactivated");
        }
        else
        {
            Debug.LogWarning("Loading panel is null");
        }

        if (raceContent != null)
        {
            raceContent.SetActive(true);
            Debug.Log("Race content activated");
        }
        else
        {
            Debug.LogWarning("Race content is null");
        }

        // Clean up data but keep what's needed for this session
        try
        {
            // Keep stake amount but clear sensitive data
            float currentStake = stakeAmount;
            PlayerPrefs.DeleteKey("EncryptedRaceToken");
            PlayerPrefs.DeleteKey("RaceToken");
            PlayerPrefs.DeleteKey("TempEncryptionKey");

            // Keep the stake amount in memory
            stakeAmount = currentStake;

            Debug.Log("Cleaned up sensitive data while preserving stake amount: " + stakeAmount);
        }
        catch (Exception ex)
        {
            Debug.LogError("Error during cleanup in ForceSkipVerification: " + ex.Message);
        }
    }

    private void CleanupSensitiveData()
    {
        try
        {
            // Clear the sensitive data after verification (successful or not)
            PlayerPrefs.DeleteKey("EncryptedStakeAmount");
            PlayerPrefs.DeleteKey("EncryptedRaceToken");
            PlayerPrefs.DeleteKey("SelectedStakeAmount");
            PlayerPrefs.DeleteKey("RaceToken");
            PlayerPrefs.DeleteKey("TempEncryptionKey");
            PlayerPrefs.Save();

            Debug.Log("All sensitive data cleared from PlayerPrefs");
        }
        catch (Exception ex)
        {
            Debug.LogError("Error during cleanup: " + ex.Message);
        }
    }

    private void LoadMainMenu()
    {
        // Clean up before loading main menu
        CleanupSensitiveData();

        // Note: We're intentionally NOT deactivating the loading panel here
        // to ensure it remains visible during the scene transition

        Debug.Log("Loading main menu scene: " + mainMenuSceneName);
        SceneManager.LoadScene(mainMenuSceneName);
    }

    private IEnumerator LoadMainMenuAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        LoadMainMenu();
    }

    // Call this when the race is completed to mark the token as used
    public void OnRaceCompleted()
    {
        if (!stakeVerified)
        {
            Debug.LogError("Cannot complete race: Stake not verified");
            return;
        }

        // Additional code to handle race completion
        // This could inform the Mirror server about the race completion
    }
}