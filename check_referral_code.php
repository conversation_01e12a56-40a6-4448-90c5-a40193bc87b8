<?php
/**
 * Check Referral Code API
 * 
 * This API checks if a referral code exists in the database
 */

// Set headers for JSON response
header('Content-Type: application/json');

// Include database connection
require_once 'inc/db.php';

// Function to validate and sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Main process
try {
    // Get JSON data from request body
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    // Check if referral code is provided
    if (!isset($data['referral_code']) || empty($data['referral_code'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Referral code is required'
        ]);
        exit;
    }
    
    // Sanitize referral code
    $referral_code = sanitizeInput($data['referral_code']);
    
    // Check if referral code exists in the database
    $stmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ?");
    $stmt->bind_param("s", $referral_code);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Referral code exists
        echo json_encode([
            'status' => 'success',
            'message' => 'Valid referral code'
        ]);
    } else {
        // Referral code does not exist
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid referral code'
        ]);
    }
    
} catch (Exception $e) {
    // Log the error
    error_log("Check referral code error: " . $e->getMessage());
    
    // Return a generic error message
    echo json_encode([
        'status' => 'error',
        'message' => 'A system error occurred. Please try again later'
    ]);
}

// Close the database connection
$conn->close();
?>
