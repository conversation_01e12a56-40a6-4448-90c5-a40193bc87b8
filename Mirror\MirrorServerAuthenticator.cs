using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq; // Add this for FirstOrDefault extension method
using UnityEngine;
using UnityEngine.Networking;
using Mirror;
using System.Security.Cryptography;
using System.Text;

/// <summary>
/// Server-side authenticator for Mirror networking
/// Handles receiving and validating authentication data from clients
/// </summary>
public class MirrorServerAuthenticator : NetworkBehaviour
{
    [SerializeField] private string verifyForMirrorUrl = "https://game-gofaster.com/gam/api/verify_for_mirror.php";
    [SerializeField] private string updateUserDataUrl = "https://game-gofaster.com/gam/api/update_user_data.php";
    [SerializeField] private string removeKey2Url = "https://game-gofaster.com/gam/api/remove_key2.php";
    
    // Network manager reference
    private NetworkManager networkManager;
    
    // Store authenticated connections and their data
    private Dictionary<NetworkConnection, AuthData> authenticatedConnections = new Dictionary<NetworkConnection, AuthData>();
    
    // Current session ID for API requests
    private string currentSessionId;
    
    // Current nonce for API requests
    private string currentNonce = "";
    
    // Structure to hold authentication data
    internal class AuthData
    {
        public string token;
        public string key1;
        public string key2;  // Only stored on server, never sent to client
        public bool isAuthenticated;
        public DateTime authTime;
        public int userId;
        public UserInfo userData;
        public string sessionId; // Add session ID to auth data
    }
    
    // User data structure (only non-sensitive data that can be sent to client)
    [Serializable]
    public class UserInfo
    {
        public string username;
        public string email;
        public string wallet_address;
        public float balance;
        public float win_balance;
        public int games_count;
        public int wins_count;
        public int losses_count;
        public object cars_data; // Using object type to handle JSON array or object
    }
    
    // Response structure for backend verification (server-side only)
    [Serializable]
    private class VerifyResponse
    {
        public string status;
        public int user_id;
        public UserInfo user;
        public string key2;  // Only received and stored on server
        public string message; // For error messages
    }
    
    // Response structure for update operations (server-side only)
    [Serializable]
    private class UpdateResponse
    {
        public string status;
        public string message;
        public string nonce;
    }
    
    void OnEnable()
    {
        // Register for connection events when enabled
        NetworkServer.OnConnectedEvent += OnServerConnected;
        NetworkServer.OnDisconnectedEvent += OnServerDisconnected;
        Debug.Log("MirrorServerAuthenticator OnEnable - Registered server connection events");
    }
    
    void OnDisable()
    {
        // Unregister when disabled to prevent multiple callbacks
        NetworkServer.OnConnectedEvent -= OnServerConnected;
        NetworkServer.OnDisconnectedEvent -= OnServerDisconnected;
        Debug.Log("MirrorServerAuthenticator OnDisable - Unregistered server connection events");
    }
    
    void Start()
    {
        // This should only be active on the server
        if (!isServer)
        {
            Debug.Log("MirrorServerAuthenticator disabled on client");
            enabled = false;
            return;
        }
        
        // Get network manager reference
        networkManager = NetworkManager.singleton;
        Debug.Log("MirrorServerAuthenticator Start - Network manager found: " + (networkManager != null));
        
        try
        {
            // Register message handler for authentication request
            // Note: false parameter allows receiving unauthenticated messages
            NetworkServer.RegisterHandler<MirrorAuthMessages.AuthRequestMessage>(OnAuthRequestMessage, false);
            Debug.Log("MirrorServerAuthenticator Start - Registered handler for auth request");
        }
        catch (Exception e)
        {
            Debug.LogError($"MirrorServerAuthenticator Start - Error registering message handler: {e.Message}");
        }
    }
    
    // Called when a client connects to the server
    private void OnServerConnected(NetworkConnection conn)
    {
        Debug.Log($"OnServerConnected - Client connected: {conn}");
        
        // Initialize authentication data for this connection with unique session ID
        string sessionId = Guid.NewGuid().ToString("N");
        Debug.Log($"Generated unique session ID for connection {conn}: {sessionId}");
        
        if (!authenticatedConnections.ContainsKey(conn))
        {
            authenticatedConnections[conn] = new AuthData
            {
                isAuthenticated = false,
                authTime = DateTime.Now,
                sessionId = sessionId // Store unique session ID for this connection
            };
            Debug.Log($"OnServerConnected - Initialized auth data for connection: {conn}");
        }
    }
    
    // Called when a client disconnects from the server
    private void OnServerDisconnected(NetworkConnection conn)
    {
        Debug.Log($"OnServerDisconnected - Client disconnected: {conn}");
        
        // Get auth data for this connection before removing
        AuthData authData = null;
        if (authenticatedConnections.TryGetValue(conn, out authData))
        {
            // If this connection was authenticated, remove the key2
            if (authData != null && authData.isAuthenticated)
            {
                Debug.Log($"OnServerDisconnected - Removing key2 for authenticated connection: {conn}");
                StartCoroutine(RemoveKey2Coroutine(authData.token, authData.sessionId));
            }
            
            // Clean up authentication data
            authenticatedConnections.Remove(conn);
            Debug.Log($"OnServerDisconnected - Removed auth data for connection: {conn}");
        }
    }
    
    // Handler for authentication request message
    private void OnAuthRequestMessage(NetworkConnection conn, MirrorAuthMessages.AuthRequestMessage msg)
    {
        try
        {
            Debug.Log($"OnAuthRequestMessage - Received auth request from connection: {conn}");
            
            // Ensure we have an entry for this connection
            if (!authenticatedConnections.ContainsKey(conn))
            {
                Debug.Log($"OnAuthRequestMessage - Connection {conn} not found in authenticatedConnections, creating entry");
                
                // Generate a unique session ID for this new connection
                string newSessionId = Guid.NewGuid().ToString("N");
                Debug.Log($"OnAuthRequestMessage - Generated new session ID for connection {conn}: {newSessionId}");
                
                authenticatedConnections[conn] = new AuthData
                {
                    isAuthenticated = false,
                    authTime = DateTime.Now,
                    sessionId = newSessionId  // Set the session ID
                };
                
                Debug.Log($"OnAuthRequestMessage - Created new auth data with session ID: {newSessionId}");
            }
            else
            {
                // Check if session ID exists
                if (string.IsNullOrEmpty(authenticatedConnections[conn].sessionId))
                {
                    string newSessionId = Guid.NewGuid().ToString("N");
                    Debug.Log($"OnAuthRequestMessage - Missing session ID for existing connection, generating new one: {newSessionId}");
                    authenticatedConnections[conn].sessionId = newSessionId;
                }
            }
            
            // Log token and key details (sanitized for security)
            if (!string.IsNullOrEmpty(msg.token))
            {
                string tokenStart = msg.token.Length > 8 ? msg.token.Substring(0, 4) : "";
                string tokenEnd = msg.token.Length > 8 ? msg.token.Substring(msg.token.Length - 4) : "";
                Debug.Log($"OnAuthRequestMessage - Token received - Length: {msg.token.Length}, Format: {tokenStart}...{tokenEnd}");
            }
            else
            {
                Debug.LogWarning("OnAuthRequestMessage - Received token is null or empty");
                SendAuthResponse(conn, false, "Token is missing");
                return;
            }
            
            if (!string.IsNullOrEmpty(msg.key1))
            {
                string key1Start = msg.key1.Length > 8 ? msg.key1.Substring(0, 4) : "";
                string key1End = msg.key1.Length > 8 ? msg.key1.Substring(msg.key1.Length - 4) : "";
                Debug.Log($"OnAuthRequestMessage - Key1 received - Length: {msg.key1.Length}, Format: {key1Start}...{key1End}");
            }
            else
            {
                Debug.LogWarning("OnAuthRequestMessage - Received key1 is null or empty");
                SendAuthResponse(conn, false, "Key1 is missing");
                return;
            }
            
            // Start verification process with backend
            StartCoroutine(VerifyWithBackend(conn, msg.token, msg.key1));
        }
        catch (Exception e)
        {
            Debug.LogError($"OnAuthRequestMessage - Exception handling auth request: {e.Message}");
            try
            {
                SendAuthResponse(conn, false, "Server error processing request");
            }
            catch (Exception ex)
            {
                Debug.LogError($"OnAuthRequestMessage - Failed to send error response: {ex.Message}");
            }
        }
    }
    
    // Verify authentication with backend server
    private IEnumerator VerifyWithBackend(NetworkConnection conn, string token, string key1)
    {
        Debug.Log($"VerifyWithBackend - Starting verification for connection: {conn}");
        
        // Check if the connection exists in the dictionary
        if (!authenticatedConnections.ContainsKey(conn))
        {
            Debug.LogError($"VerifyWithBackend - Connection {conn} not found in authenticatedConnections dictionary");
            SendAuthResponse(conn, false, "Internal server error: Connection data missing");
            yield break;
        }
        
        // Get the specific session ID for this connection
        string sessionId = authenticatedConnections[conn].sessionId;
        
        // Validate session ID is not null or empty
        if (string.IsNullOrEmpty(sessionId))
        {
            // Generate a new session ID if one doesn't exist
            sessionId = Guid.NewGuid().ToString("N");
            Debug.LogWarning($"VerifyWithBackend - Session ID was empty, generated new one: {sessionId}");
            authenticatedConnections[conn].sessionId = sessionId;
        }
        
        Debug.Log($"VerifyWithBackend - Using session ID: {sessionId} for connection: {conn}");
        
        // Create form data for backend request
        WWWForm form = new WWWForm();
        form.AddField("token", token);
        form.AddField("key1", key1);
        form.AddField("session_id", sessionId);
        
        Debug.Log($"VerifyWithBackend - Including session_id: {sessionId}");
        
        // Send request to backend
        UnityWebRequest www = UnityWebRequest.Post(verifyForMirrorUrl, form);
        
        try {
            // Set longer timeout and headers
            www.timeout = 30; // 30 seconds timeout
            www.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            www.SetRequestHeader("Accept", "application/json");
            
            Debug.Log($"VerifyWithBackend - Sending request to {verifyForMirrorUrl}");
        }
        catch (Exception e)
        {
            Debug.LogError($"VerifyWithBackend - Exception setting up request: {e.Message}");
            SendAuthResponse(conn, false, "Server error setting up request");
            yield break;
        }
        
        // Send the request
        yield return www.SendWebRequest();
        
        try {
            Debug.Log($"VerifyWithBackend - Request completed: Result={www.result}, ResponseCode={www.responseCode}");
            
            // Get response text
            string responseText = www.downloadHandler.text;
            Debug.Log($"VerifyWithBackend - Response text: {responseText}");
            
            if (www.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"VerifyWithBackend - Request failed: {www.error}");
                SendAuthResponse(conn, false, $"Backend request failed: {www.error}");
                yield break;
            }
            
            if (string.IsNullOrEmpty(responseText))
            {
                Debug.LogError("VerifyWithBackend - Empty response from server");
                SendAuthResponse(conn, false, "Empty response from server");
                yield break;
            }
            
            try
            {
                // Try to parse response
                var response = JsonUtility.FromJson<VerifyResponse>(responseText);
                
                Debug.Log($"VerifyWithBackend - Parsed response: Status={response.status}, UserID={response.user_id}");
                
                if (response.status == "success")
                {
                    // Check if key2 is present
                    if (string.IsNullOrEmpty(response.key2))
                    {
                        Debug.LogError("VerifyWithBackend - Success response but key2 is missing");
                        SendAuthResponse(conn, false, "Server error: Missing key2");
                        yield break;
                    }
                    
                    // Add detailed logging for key2
                    string key2Start = response.key2.Length > 8 ? response.key2.Substring(0, 4) : "";
                    string key2End = response.key2.Length > 8 ? response.key2.Substring(response.key2.Length - 4) : "";
                    Debug.Log($"VerifyWithBackend - Received Key2 - Length: {response.key2.Length}, Format: {key2Start}...{key2End}");
                    
                    // Store authentication data - first check and potentially create the dictionary entry
                    if (!authenticatedConnections.ContainsKey(conn))
                    {
                        Debug.LogWarning($"VerifyWithBackend - Connection {conn} not in authenticatedConnections dictionary, creating it now");
                        
                        // Generate a new session ID if we're creating a new auth data object
                        string newSessionId = Guid.NewGuid().ToString("N");
                        Debug.Log($"VerifyWithBackend - Generated new session ID for connection {conn}: {newSessionId}");
                        
                        authenticatedConnections[conn] = new AuthData
                        {
                            isAuthenticated = false,
                            authTime = DateTime.Now,
                            sessionId = newSessionId // Make sure we set a sessionId
                        };
                    }
                    
                    // Store the session ID from the response if it was provided
                    string responseSessionId = null;
                    try
                    {
                        // Check if the response includes a session_id field
                        if (responseText.Contains("session_id"))
                        {
                            // Try to extract the session ID from the raw response
                            var startIdx = responseText.IndexOf("\"session_id\":");
                            if (startIdx > 0)
                            {
                                startIdx += 13; // Length of "session_id":
                                var endIdx = responseText.IndexOf("\"", startIdx + 1);
                                if (endIdx > startIdx)
                                {
                                    responseSessionId = responseText.Substring(startIdx, endIdx - startIdx).Trim('"', ' ');
                                    Debug.Log($"VerifyWithBackend - Extracted session ID from response: {responseSessionId}");
                                    
                                    // Update our sessionId if it's valid
                                    if (!string.IsNullOrEmpty(responseSessionId))
                                    {
                                        authenticatedConnections[conn].sessionId = responseSessionId;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"VerifyWithBackend - Failed to extract session ID from response: {e.Message}");
                    }
                    
                    // Now we can safely update the authentication data
                    authenticatedConnections[conn].token = token;
                    authenticatedConnections[conn].key1 = key1;
                    authenticatedConnections[conn].key2 = response.key2;  // Store key2 securely on server
                    authenticatedConnections[conn].isAuthenticated = true;
                    authenticatedConnections[conn].authTime = DateTime.Now;
                    authenticatedConnections[conn].userId = response.user_id;
                    authenticatedConnections[conn].userData = response.user;
                    // sessionId is already set
                    
                    Debug.Log($"VerifyWithBackend - Authentication successful for connection: {conn}");
                    
                    if (response.user != null)
                    {
                        Debug.Log($"VerifyWithBackend - User authenticated: {response.user.username} (ID: {response.user_id})");
                    }
                    
                    // Send success response to client (without key2)
                    SendAuthResponse(conn, true, "Authentication successful");
                }
                else
                {
                    // Check for disconnect_required message
                    if (response.message == "disconnect_required")
                    {
                        Debug.LogWarning($"VerifyWithBackend - Disconnect required for user. Server couldn't generate key after retry.");
                        SendAuthResponse(conn, false, "Disconnect required: Server couldn't generate key");
                        
                        // Start disconnection process
                        StartCoroutine(DisconnectAfterDelay(conn, 1.0f));
                    }
                    else
                    {
                        Debug.LogError($"VerifyWithBackend - Backend verification failed: {response.status}");
                        SendAuthResponse(conn, false, "Authentication failed: " + (response.message ?? "Unknown error"));
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"VerifyWithBackend - Error parsing response: {e.Message}");
                SendAuthResponse(conn, false, "Error processing authentication response");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"VerifyWithBackend - Exception processing response: {e.Message}");
            SendAuthResponse(conn, false, "Server error processing response");
        }
        finally
        {
            www.Dispose();
        }
    }
    
    // Send authentication response to client (without key2)
    private void SendAuthResponse(NetworkConnection conn, bool success, string message)
    {
        try
        {
            Debug.Log($"SendAuthResponse - Sending response to connection: {conn} - Success: {success}");
            
            MirrorAuthMessages.AuthResponseMessage response = new MirrorAuthMessages.AuthResponseMessage
            {
                success = success,
                message = message
            };
            
            conn.Send(response);
            Debug.Log($"SendAuthResponse - Response sent to connection: {conn}");
            
            // Disconnect if authentication failed
            if (!success)
            {
                Debug.Log($"SendAuthResponse - Authentication failed, disconnecting connection: {conn}");
                // Add a small delay before disconnecting to ensure message gets sent
                StartCoroutine(DisconnectAfterDelay(conn, 0.5f));
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"SendAuthResponse - Exception sending auth response: {e.Message}");
        }
    }
    
    // Disconnect after a short delay
    private IEnumerator DisconnectAfterDelay(NetworkConnection conn, float delay)
    {
        yield return new WaitForSeconds(delay);
        if (NetworkServer.active && conn != null)
        {
            conn.Disconnect();
            Debug.Log($"DisconnectAfterDelay - Disconnected connection: {conn}");
        }
    }
    
    // Check if a connection is authenticated
    public bool IsAuthenticated(NetworkConnection conn)
    {
        bool isAuth = authenticatedConnections.ContainsKey(conn) && authenticatedConnections[conn].isAuthenticated;
        Debug.Log($"IsAuthenticated - Connection {conn} is authenticated: {isAuth}");
        return isAuth;
    }
    
    // Get authentication data for a connection (server-side only)
    internal AuthData GetAuthData(NetworkConnection conn)
    {
        if (authenticatedConnections.ContainsKey(conn))
        {
            Debug.Log($"GetAuthData - Retrieved auth data for connection: {conn}");
            return authenticatedConnections[conn];
        }
        Debug.LogWarning($"GetAuthData - No auth data found for connection: {conn}");
        return null;
    }
    
    // Update user data at the end of a match (server-side only)
    public void UpdateUserData(NetworkConnection conn, Dictionary<string, object> dataToUpdate)
    {
        if (!IsAuthenticated(conn))
        {
            Debug.LogError($"UpdateUserData - Cannot update data for unauthenticated connection: {conn}");
            return;
        }
        
        // Get authentication info
        var authData = GetAuthData(conn);
        if (authData == null)
        {
            Debug.LogError($"UpdateUserData - AuthData is null for connection: {conn}");
            return;
        }
        
        Debug.Log($"UpdateUserData - Updating data for connection: {conn} - User ID: {authData.userId}");
        
        // Start update process
        StartCoroutine(UpdateUserDataCoroutine(conn, authData, dataToUpdate));
    }
    
    // Update user data with PHP backend (server-side only)
    private IEnumerator UpdateUserDataCoroutine(NetworkConnection conn, AuthData authData, Dictionary<string, object> dataToUpdate)
    {
        if (string.IsNullOrEmpty(authData.key2))
        {
            Debug.LogError("UpdateUserDataCoroutine - key2 is null or empty, cannot proceed with update");
            yield break;
        }

        // Add detailed logging for key2 being used in update
        string key2Start = authData.key2.Length > 8 ? authData.key2.Substring(0, 4) : "";
        string key2End = authData.key2.Length > 8 ? authData.key2.Substring(authData.key2.Length - 4) : "";
        Debug.Log($"UpdateUserDataCoroutine - Using Key2 - Length: {authData.key2.Length}, Format: {key2Start}...{key2End}");

        Debug.Log($"UpdateUserDataCoroutine - Starting update for user ID: {authData.userId}");
        
        // Create form data for the request
        WWWForm form = new WWWForm();
        form.AddField("token", authData.token);
        form.AddField("key1", authData.key1);
        form.AddField("key2", authData.key2);
        form.AddField("session_id", authData.sessionId);
        
        // Generate a nonce for the request
        string nonce = GenerateNonce();
        form.AddField("nonce", nonce);
        
        // Create nested JSON format {"data":{...}} as expected by the PHP script
        string innerJson = "{";
        bool first = true;
        foreach (var kvp in dataToUpdate)
        {
            if (!first) innerJson += ",";
            // Handle different value types
            if (kvp.Value is string)
            {
                innerJson += $"\"{kvp.Key}\":\"{kvp.Value}\"";
            }
            else
            {
                innerJson += $"\"{kvp.Key}\":{kvp.Value}";
            }
            first = false;
        }
        innerJson += "}";
        
        string nestedJson = "{\"data\":" + innerJson + "}";
        form.AddField("data", nestedJson);
        Debug.Log($"UpdateUserDataCoroutine - Data JSON: {nestedJson}");
        
        // Send the request
        UnityWebRequest www = UnityWebRequest.Post(updateUserDataUrl, form);
        www.timeout = 30; // 30 seconds timeout
        www.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        www.SetRequestHeader("Accept", "application/json");
        
        yield return www.SendWebRequest();
        
        Debug.Log($"UpdateUserDataCoroutine - Response: Result={www.result}, Code={www.responseCode}");
        
        string responseText = www.downloadHandler.text;
        Debug.Log($"UpdateUserDataCoroutine - Response text: {responseText}");
        
        if (www.result == UnityWebRequest.Result.Success)
        {
            Debug.Log("UpdateUserDataCoroutine - Update successful!");
            
            try
            {
                // Parse the response
                UpdateResponse response = JsonUtility.FromJson<UpdateResponse>(responseText);
                if (response != null && response.status == "success")
                {
                    Debug.Log("User data updated successfully!");
                    
                    // Update local user data
                    foreach (var item in dataToUpdate)
                    {
                        switch (item.Key)
                        {
                            case "balance":
                                authData.userData.balance = Convert.ToSingle(item.Value);
                                break;
                            case "win_balance":
                                authData.userData.win_balance = Convert.ToSingle(item.Value);
                                break;
                            case "games_count":
                                authData.userData.games_count = Convert.ToInt32(item.Value);
                                break;
                            case "wins_count":
                                authData.userData.wins_count = Convert.ToInt32(item.Value);
                                break;
                            case "losses_count":
                                authData.userData.losses_count = Convert.ToInt32(item.Value);
                                break;
                        }
                    }
                    
                    // Store new nonce if one was provided
                    if (!string.IsNullOrEmpty(response.nonce))
                    {
                        currentNonce = response.nonce;
                    }
                }
                else
                {
                    Debug.LogWarning($"API responded with status: {(response != null ? response.status : "unknown")}");
                    Debug.LogWarning($"API message: {(response != null ? response.message : "No message")}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Error parsing API response: {e.Message}");
            }
        }
        else
        {
            Debug.LogError($"UpdateUserDataCoroutine - Update failed: {www.error}");
            Debug.LogError($"UpdateUserDataCoroutine - Response: {responseText}");
        }
        
        www.Dispose();
    }
    
    // Generate a random nonce for API requests
    private string GenerateNonce()
    {
        var random = new System.Random();
        var randomBytes = new byte[16];
        random.NextBytes(randomBytes);
        return BitConverter.ToString(randomBytes).Replace("-", "").ToLower();
    }
    
    // Get user data for a connection
    public UserInfo GetUserData(NetworkConnection conn)
    {
        var authData = GetAuthData(conn);
        if (authData != null && authData.isAuthenticated)
        {
            Debug.Log($"GetUserData - Retrieved user data for connection: {conn}");
            return authData.userData;
        }
        Debug.LogWarning($"GetUserData - No user data found for connection: {conn}");
        return null;
    }
    
    // Clean up all user data when server stops or application quits
    public void CleanupAllUserData()
    {
        Debug.Log("CleanupAllUserData - Cleaning up all user data");
        authenticatedConnections.Clear();
    }
    
    // Clean up when server stops
    void OnDestroy()
    {
        Debug.Log("MirrorServerAuthenticator OnDestroy - Cleaning up server authenticator");
        CleanupAllUserData();
    }
    
    // Coroutine to remove key2 when connection is disconnected
    private IEnumerator RemoveKey2Coroutine(string token, string sessionId)
    {
        if (string.IsNullOrEmpty(token))
        {
            Debug.LogWarning("RemoveKey2Coroutine - Token is null or empty, cannot remove key2");
            yield break;
        }

        Debug.Log($"RemoveKey2Coroutine - Removing key2 for token: {token.Substring(0, 10)}... with session ID: {sessionId}");
        
        // Create form data for the request
        WWWForm form = new WWWForm();
        form.AddField("token", token);
        form.AddField("session_id", sessionId);
        
        // Send the request
        UnityWebRequest www = UnityWebRequest.Post(removeKey2Url, form);
        www.timeout = 10; // 10 seconds timeout
        www.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        www.SetRequestHeader("Accept", "application/json");
        
        yield return www.SendWebRequest();
        
        if (www.result == UnityWebRequest.Result.Success)
        {
            string responseText = www.downloadHandler.text;
            Debug.Log($"RemoveKey2Coroutine - Response: {responseText}");
            Debug.Log("Key2 removed successfully");
        }
        else
        {
            Debug.LogError($"RemoveKey2Coroutine - Failed to remove key2: {www.error}");
            Debug.LogError($"RemoveKey2Coroutine - Response: {www.downloadHandler.text}");
        }
        
        www.Dispose();
    }
} 