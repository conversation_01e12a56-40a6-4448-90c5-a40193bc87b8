using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using Mirror;

/// <summary>
/// Client-side authenticator for Mirror networking
/// <PERSON>les sending authentication data to the Mirror server
/// </summary>
public class MirrorClientAuthenticator : NetworkBehaviour
{
    // Network manager reference
    private NetworkManager networkManager;
    
    // Use shared message types from MirrorAuthMessages
    private void OnAuthResponseMessage(MirrorAuthMessages.AuthResponseMessage msg)
    {
        Debug.Log($"[DEBUG] OnAuthResponseMessage - Auth response received: {(msg.success ? "Success" : "Failed")} - {msg.message}");
        
        if (!msg.success)
        {
            // Authentication failed, disconnect
            Debug.LogError($"[DEBUG] OnAuthResponseMessage - Authentication failed: {msg.message}");
            NetworkClient.Disconnect();
            
            // Notify UI or game manager about the failure
            // You could use events, callbacks, or direct references here
            // Example: GameManager.Instance.OnAuthFailed(msg.message);
        }
        else
        {
            // Authentication succeeded
            Debug.Log("[DEBUG] OnAuthResponseMessage - Authentication successful");
            
            // Notify game that authentication is complete
            // Example: GameManager.Instance.OnAuthSuccess();
        }
    }
    
    void OnEnable()
    {
        // Register for connection events when enabled
        NetworkClient.OnConnectedEvent += OnClientConnected;
        Debug.Log("[DEBUG] MirrorClientAuthenticator OnEnable - Registered OnConnectedEvent callback");
    }
    
    void OnDisable()
    {
        // Unregister when disabled to prevent multiple callbacks
        NetworkClient.OnConnectedEvent -= OnClientConnected;
        Debug.Log("[DEBUG] MirrorClientAuthenticator OnDisable - Unregistered OnConnectedEvent callback");
    }
    
    void Start()
    {
        // This should only be active on the client
        if (isServer && !isClient)
        {
            Debug.Log("MirrorClientAuthenticator disabled on server-only instance");
            enabled = false;
            return;
        }
        
        // Get network manager reference
        networkManager = NetworkManager.singleton;
        Debug.Log("[DEBUG] MirrorClientAuthenticator Start - Network manager found: " + (networkManager != null));
   
        // Register message handlers using the same message IDs across client and server
        // Important: we need to use the same message IDs on both sides
        NetworkClient.RegisterHandler<MirrorAuthMessages.AuthResponseMessage>(OnAuthResponseMessage);
        Debug.Log("[DEBUG] MirrorClientAuthenticator Start - Registered handler for auth response");

        // If already connected, send auth
        if (NetworkClient.isConnected)
        {
            Debug.Log("[DEBUG] MirrorClientAuthenticator Start - Client already connected, sending auth");
            SendAuthRequest();
        }
    }
    
    /// <summary>
    /// Connect to server and send authentication data
    /// </summary>
    public void ConnectToServer()
    {
        Debug.Log("[DEBUG] ConnectToServer - Starting connection process");
        
        if (!NetworkClient.active)
        {
            // Get current network manager settings
            if (networkManager == null)
                networkManager = NetworkManager.singleton;
                
            if (networkManager == null)
            {
                Debug.LogError("[DEBUG] ConnectToServer - NetworkManager not found!");
                return;
            }
            
            Debug.Log($"[DEBUG] ConnectToServer - Using NetworkManager settings: Address={networkManager.networkAddress}, Transport={networkManager.transport.GetType().Name}");
            
            // Start as client using Mirror's network manager
            Debug.Log("[DEBUG] ConnectToServer - Starting client");
            NetworkClient.Connect(networkManager.networkAddress);
        }
        else
        {
            Debug.LogWarning("[DEBUG] ConnectToServer - Client is already active");
            
            // If already connected but not authenticated, send auth again
            if (NetworkClient.isConnected)
            {
                Debug.Log("[DEBUG] ConnectToServer - Client already connected, sending auth");
                SendAuthRequest();
            }
        }
    }
    
    // Called when client connects to the server
    private void OnClientConnected()
    {
        Debug.Log("[DEBUG] OnClientConnected - Connected to server, preparing auth request");
        SendAuthRequest();
    }
    
    // Separate method to send auth request, can be called from multiple places
    private void SendAuthRequest()
    {
        if (!NetworkClient.isConnected)
        {
            Debug.LogError("[DEBUG] SendAuthRequest - Not connected to server! Cannot send auth request.");
            return;
        }
        
        // Get authentication data from session manager
        string token = GetAuthToken();
        string key1 = GetKey1();
        
        // Log token and key details (sanitized for security)
        if (!string.IsNullOrEmpty(token))
        {
            string tokenStart = token.Length > 8 ? token.Substring(0, 4) : "";
            string tokenEnd = token.Length > 8 ? token.Substring(token.Length - 4) : "";
            Debug.Log($"[DEBUG] SendAuthRequest - Token retrieved - Length: {token.Length}, Format: {tokenStart}...{tokenEnd}");
        }
        else
        {
            Debug.LogError("[DEBUG] SendAuthRequest - Token is empty or null!");
            return; // Don't send empty auth
        }
        
        if (!string.IsNullOrEmpty(key1))
        {
            string key1Start = key1.Length > 8 ? key1.Substring(0, 4) : "";
            string key1End = key1.Length > 8 ? key1.Substring(key1.Length - 4) : "";
            Debug.Log($"[DEBUG] SendAuthRequest - Key1 retrieved - Length: {key1.Length}, Format: {key1Start}...{key1End}");
        }
        else
        {
            Debug.LogError("[DEBUG] SendAuthRequest - Key1 is empty or null!");
            return; // Don't send empty auth
        }
        
        try
        {
            // Send authentication request message
            MirrorAuthMessages.AuthRequestMessage authMessage = new MirrorAuthMessages.AuthRequestMessage
            {
                token = token,
                key1 = key1
            };
            
            Debug.Log("[DEBUG] SendAuthRequest - Sending auth request to server");
            NetworkClient.Send(authMessage);
            Debug.Log("[DEBUG] SendAuthRequest - Auth request sent to server");
            
            // Try sending again after a short delay as a fallback (for some network conditions)
            StartCoroutine(RetrySendAuthAfterDelay(2.0f, token, key1));
        }
        catch (Exception e)
        {
            Debug.LogError($"[DEBUG] SendAuthRequest - Exception when sending auth request: {e.Message}");
        }
    }
    
    // Retry sending auth after a delay (helpful in some network conditions)
    private IEnumerator RetrySendAuthAfterDelay(float delay, string token, string key1)
    {
        yield return new WaitForSeconds(delay);
        
        // Only retry if still connected but not authenticated
        // (We would need a way to check if authenticated, for now just retry once)
        if (NetworkClient.isConnected)
        {
            Debug.Log("[DEBUG] RetrySendAuthAfterDelay - Resending auth as a fallback");
            
            MirrorAuthMessages.AuthRequestMessage authMessage = new MirrorAuthMessages.AuthRequestMessage
            {
                token = token,
                key1 = key1
            };
            
            NetworkClient.Send(authMessage);
            Debug.Log("[DEBUG] RetrySendAuthAfterDelay - Auth request resent to server");
        }
    }
    
    // Get JWT token from SessionManager or Login
    private string GetAuthToken()
    {
        Debug.Log("[DEBUG] GetAuthToken - Getting authentication token");
        
        // Try getting token from SessionManager first
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            Debug.Log("[DEBUG] GetAuthToken - Token retrieved from SessionManager");
            return SessionManager.Instance.JWT_Token;
        }
        // Fallback to Login static property
        else if (!string.IsNullOrEmpty(Login.JWT_Token))
        {
            Debug.Log("[DEBUG] GetAuthToken - Token retrieved from Login static property (fallback)");
            return Login.JWT_Token;
        }
        else
        {
            Debug.LogError("[DEBUG] GetAuthToken - No authentication token available. Please log in first.");
            return "";
        }
    }
    
    // Get Key1 from SessionManager or Login
    private string GetKey1()
    {
        Debug.Log("[DEBUG] GetKey1 - Getting Key1");
        
        // Try getting Key1 from SessionManager first
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            Debug.Log("[DEBUG] GetKey1 - Key1 retrieved from SessionManager");
            return SessionManager.Instance.Key1;
        }
        // Fallback to Login static property
        else if (!string.IsNullOrEmpty(Login.Key1))
        {
            Debug.Log("[DEBUG] GetKey1 - Key1 retrieved from Login static property (fallback)");
            return Login.Key1;
        }
        else
        {
            Debug.LogError("[DEBUG] GetKey1 - No Key1 available. Please log in first.");
            return "";
        }
    }
    
    // Disconnect from server
    public void DisconnectFromServer()
    {
        if (NetworkClient.active)
        {
            Debug.Log("[DEBUG] DisconnectFromServer - Disconnecting from server");
            NetworkClient.Disconnect();
            Debug.Log("[DEBUG] DisconnectFromServer - Disconnected from server");
        }
    }
} 