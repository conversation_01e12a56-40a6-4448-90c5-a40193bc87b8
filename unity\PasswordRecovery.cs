using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using TMPro;
using UnityEngine.UI;

public class PasswordRecovery : MonoBehaviour
{
    [SerializeField] private TMP_InputField emailInput;
    [SerializeField] private TextMesh<PERSON>roUGUI statusText;
    [SerializeField] private Button submitButton;
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/password_recovery.php";

    private void Start()
    {
        // Clear status text on start
        if (statusText != null)
            statusText.text = "";

        // Add listener to submit button
        if (submitButton != null)
            submitButton.onClick.AddListener(OnSubmitButtonClick);
    }

    public void OnSubmitButtonClick()
    {
        if (emailInput == null || string.IsNullOrWhiteSpace(emailInput.text))
        {
            if (statusText != null)
                statusText.text = "Please enter your email";
            return;
        }

        // Validate email format
        if (!IsValidEmail(emailInput.text))
        {
            if (statusText != null)
                statusText.text = "Please enter a valid email";
            return;
        }

        // Disable submit button to prevent multiple requests
        if (submitButton != null)
            submitButton.interactable = false;

        // Start password recovery process
        StartCoroutine(PasswordRecoveryCoroutine());
    }

    private IEnumerator PasswordRecoveryCoroutine()
    {
        if (statusText != null)
            statusText.text = "Sending request...";

        // Create form data
        Dictionary<string, string> formData = new Dictionary<string, string>
        {
            { "email", emailInput.text }
        };

        // Create web request
        UnityWebRequest request = UnityWebRequest.Post(apiUrl, formData);
        request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

        // Send request
        yield return request.SendWebRequest();

        // Get the response text regardless of success or failure
        string responseText = request.downloadHandler.text;

        // Log detailed response for debugging
        Debug.Log($"Password recovery response: Code={request.responseCode}, Text={responseText}");

        // Re-enable submit button
        if (submitButton != null)
            submitButton.interactable = true;

        try
        {
            // Try to parse the response
            if (!string.IsNullOrEmpty(responseText))
            {
                PasswordRecoveryResponse response = JsonUtility.FromJson<PasswordRecoveryResponse>(responseText);

                if (response != null)
                {
                    // Check response status
                    if (response.status == "success")
                    {
                        if (statusText != null)
                            statusText.text = "Password reset link has been sent to your email";
                    }
                    else
                    {
                        // Show the error message from the server
                        if (statusText != null)
                            statusText.text = "Error: " + response.message;
                        Debug.LogError("Password recovery failed: " + response.message);
                    }

                    yield break;
                }
            }

            // If we get here, either responseText was empty or parsing failed
            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Password Recovery Error: " + request.error);
                Debug.LogError("Response code: " + request.responseCode);
                Debug.LogError("Response text: " + responseText);

                if (statusText != null)
                {
                    if (request.responseCode == 500)
                        statusText.text = "Server error. Please contact support.";
                    else
                        statusText.text = "Connection error: " + request.error;
                }
            }
            else
            {
                // This is a fallback in case the response doesn't match our expected format
                if (statusText != null)
                    statusText.text = "Invalid server response";
                Debug.LogError("Unexpected server response format");
                Debug.LogError("Response text: " + responseText);
            }
        }
        catch (Exception ex)
        {
            if (statusText != null)
                statusText.text = "Error: " + (ex.Message.Length > 50 ? ex.Message.Substring(0, 50) + "..." : ex.Message);
            Debug.LogError("Error parsing password recovery response: " + ex.Message);
        }
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    [Serializable]
    private class PasswordRecoveryResponse
    {
        public string status;
        public string message;
    }
}
