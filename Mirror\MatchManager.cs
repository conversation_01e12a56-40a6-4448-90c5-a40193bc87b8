using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Mirror;

/// <summary>
/// Manages a match between players
/// Updates player stats at the end of the match
/// </summary>
public class MatchManager : NetworkBehaviour
{
    [Header("Match Settings")]
    [SerializeField] private float matchDuration = 300f; // 5 minutes
    [SerializeField] private float timeToStart = 10f;
    
    [Header("UI")]
    [SerializeField] private GameObject matchUI;
    [SerializeField] private TMPro.TextMeshProUGUI timerText;
    [SerializeField] private TMPro.TextMeshProUGUI statusText;
    
    // Match state
    private bool matchStarted = false;
    private bool matchEnded = false;
    private float matchTimer = 0f;
    private float startTimer = 0f;
    
    // Players in the match
    private List<PlayerNetworkObject> players = new List<PlayerNetworkObject>();
    
    // Currently winning player
    private PlayerNetworkObject currentWinner = null;
    
    // Start is called before the first frame update
    void Awake()
    {
        // This should only be active on the server
        if (!isServer)
        {
            Debug.Log("MatchManager disabled on client instance");
            enabled = false;
            gameObject.SetActive(false);
            return;
        }
    }
    
    void Start()
    {
        if (!isServer)
        {
            enabled = false;
            return;
        }
        
        // Initialize the match
        matchStarted = false;
        matchEnded = false;
        startTimer = timeToStart;
        matchTimer = matchDuration;
        
        // Update UI
        UpdateUI();
        
        // Find players in the scene
        FindPlayers();
    }
    
    // Update is called once per frame
    void Update()
    {
        if (!isServer)
            return;
            
        if (!matchStarted && !matchEnded)
        {
            // Countdown to match start
            startTimer -= Time.deltaTime;
            
            if (startTimer <= 0)
            {
                StartMatch();
            }
            
            // Update UI
            UpdateUI();
        }
        else if (matchStarted && !matchEnded)
        {
            // Match in progress
            matchTimer -= Time.deltaTime;
            
            if (matchTimer <= 0)
            {
                EndMatch();
            }
            
            // Update UI
            UpdateUI();
        }
    }
    
    /// <summary>
    /// Find all player objects in the scene
    /// </summary>
    private void FindPlayers()
    {
        players.Clear();
        
        // Find all player network objects
        var playerObjects = FindObjectsByType<PlayerNetworkObject>(FindObjectsSortMode.None);
        
        foreach (var player in playerObjects)
        {
            players.Add(player);
        }
        
        Debug.Log($"Found {players.Count} players in the match");
    }
    
    /// <summary>
    /// Start the match
    /// </summary>
    private void StartMatch()
    {
        matchStarted = true;
        matchEnded = false;
        
        Debug.Log("Match started!");
        
        // Set match status
        if (statusText != null)
        {
            statusText.text = "Match in progress";
        }
        
        // You could add code here to notify players that the match has started
    }
    
    /// <summary>
    /// End the match and update player stats
    /// </summary>
    private void EndMatch()
    {
        matchStarted = false;
        matchEnded = true;
        
        Debug.Log("Match ended!");
        
        // Find the winner (in a real game, this would be determined by game logic)
        DetermineWinner();
        
        // Update player stats
        UpdatePlayerStats();
        
        // Set match status
        if (statusText != null)
        {
            statusText.text = currentWinner != null ? 
                $"Match ended! Winner: Player {players.IndexOf(currentWinner) + 1}" : 
                "Match ended! No winner";
        }
        
        // Schedule scene change or match restart
        StartCoroutine(EndMatchCoroutine());
    }
    
    /// <summary>
    /// Determine the winner of the match
    /// In a real game, this would be based on game-specific logic
    /// </summary>
    private void DetermineWinner()
    {
        // This is just a placeholder - in a real game, you would have
        // game-specific logic to determine the winner
        
        if (players.Count > 0)
        {
            // For demonstration, just pick a random player as the winner
            int winnerIndex = Random.Range(0, players.Count);
            currentWinner = players[winnerIndex];
            
            Debug.Log($"Player {winnerIndex + 1} is the winner!");
        }
        else
        {
            currentWinner = null;
            Debug.Log("No players in the match!");
        }
    }
    
    /// <summary>
    /// Update player stats based on match results
    /// </summary>
    private void UpdatePlayerStats()
    {
        foreach (var player in players)
        {
            bool isWinner = (player == currentWinner);
            
            // Update player stats (games played, wins, losses)
            player.UpdatePlayerStats(true, isWinner);
            
            // If winner, update win balance
            if (isWinner)
            {
                // Add 10 to win_balance as a reward for winning
                player.UpdateWinBalance(10f);
            }
        }
    }
    
    /// <summary>
    /// Update the match UI
    /// </summary>
    private void UpdateUI()
    {
        if (timerText != null)
        {
            if (!matchStarted && !matchEnded)
            {
                // Show countdown to match start
                timerText.text = $"Starting in: {Mathf.CeilToInt(startTimer)}";
            }
            else if (matchStarted && !matchEnded)
            {
                // Show match timer
                timerText.text = $"Time: {Mathf.CeilToInt(matchTimer)}";
            }
            else
            {
                // Match ended
                timerText.text = "Match ended";
            }
        }
    }
    
    /// <summary>
    /// Coroutine to handle end of match logic
    /// </summary>
    private IEnumerator EndMatchCoroutine()
    {
        // Wait for a few seconds to show the results
        yield return new WaitForSeconds(5.0f);
        
        // Here you could add code to return to a lobby scene
        // or restart the match
        
        Debug.Log("Match cleanup complete");
    }
    
    /// <summary>
    /// Add a player to the match
    /// </summary>
    public void AddPlayer(PlayerNetworkObject player)
    {
        if (!players.Contains(player))
        {
            players.Add(player);
            Debug.Log($"Player added to match. Total players: {players.Count}");
        }
    }
    
    /// <summary>
    /// Remove a player from the match
    /// </summary>
    public void RemovePlayer(PlayerNetworkObject player)
    {
        if (players.Contains(player))
        {
            players.Remove(player);
            Debug.Log($"Player removed from match. Total players: {players.Count}");
            
            // If this was the winner, clear the winner
            if (player == currentWinner)
            {
                currentWinner = null;
            }
        }
    }
} 