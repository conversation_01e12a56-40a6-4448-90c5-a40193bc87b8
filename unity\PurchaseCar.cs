using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Networking;
using TMPro;
using UnityEngine.UI;
using System.Text;
using Newtonsoft.Json;
using System.Security.Cryptography;
using CodeStage.AntiCheat.ObscuredTypes;

/// <summary>
/// PurchaseCar handles vehicle purchasing, customization, and management.
/// This class has been refactored to improve organization and reduce code complexity.
/// </summary>
public class PurchaseCar : MonoBehaviour
{
    #region Serialized Fields

    [Header("UI Elements")]
    [SerializeField] private TextMeshProUGUI priceText;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private TextMeshProUGUI balanceText;
    [SerializeField] private TextMeshProUGUI ownedCarsText;
    [SerializeField] private Button purchaseButton;
    [SerializeField] private Button activateButton;
    [SerializeField] private Button nextButton;
    [SerializeField] private Button purchaseWithReferralButton;
    [SerializeField] private Button refreshButton;
    [SerializeField] private TextMeshProUGUI referralBalanceText;
    [SerializeField] private GameObject loadingPay;
    [SerializeField] private GameObject panelBuy;

    [Header("API Configuration")]
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/purchase_car.php";
    [SerializeField] private string userDataApiUrl = "https://game-gofaster.com/gam/api/get_user_data.php";
    [SerializeField] private GameObject sessionManagerPrefab;

    [Header("Vehicle Settings")]
    [SerializeField] public VehicleSetting[] vehicleSetting;

    // Vehicle UI Elements
    [SerializeField] private Button nextVehicleButton;
    [SerializeField] private Button previousVehicleButton;
    [SerializeField] private Slider vehicleSpeedSlider;
    [SerializeField] private Slider vehicleBrakingSlider;
    [SerializeField] private Slider vehicleNitroSlider;
    [SerializeField] private Slider vehicleRpmSlider;
    [SerializeField] private TextMeshProUGUI vehicleNameText;
    [SerializeField] private GameObject customizeVehiclePanel;
    [SerializeField] private GameObject BayPanel; // Panel that should be disabled when customizeVehiclePanel is active

    #endregion

    #region Private Fields

    // کلید رمزنگاری که از سرور دریافت می‌شود
    private string encryptionKey = "";

    // Car prices dictionary (matching server-side prices)
    private Dictionary<string, float> carPrices = new Dictionary<string, float>();

    private Dictionary<string, string> carNames = new Dictionary<string, string>();

    // Dictionary to track owned cars
    private Dictionary<string, bool> ownedCars = new Dictionary<string, bool>();

    // Vehicle selection variables
    private int currentVehicleNumber = 0;
    private VehicleSetting currentVehicle;
    private bool randomColorActive = false;
    private Color mainColor;

    // Store original colors of materials when the game starts
    private Dictionary<int, Color> originalBodyColors = new Dictionary<int, Color>();
    private Dictionary<int, Color> originalBodyPartsColors = new Dictionary<int, Color>();

    #endregion

    #region Enums

    // Operation types for the API
    private enum ApiOperation
    {
        FetchUserData,
        PurchaseCar,
        ActivateCar,
        GetAllCars
    }

    // Variable to track which part is currently selected for coloring
    private enum ColorTarget { Body, BodyParts }
    private ColorTarget currentColorTarget = ColorTarget.Body;

    #endregion

    #region Data Models

    // User data model
    [System.Serializable]
    public class UserData
    {
        [JsonProperty("username")]
        public string username;

        [JsonProperty("balance")]
        public float? balance;

        [JsonProperty("referral_balance")]
        public float? referral_balance;

        [JsonProperty("cars_data")]
        public string[] cars_data; // Old format

        [JsonProperty("user_cars")]
        public UserCarData[] user_cars; // New format - array of user cars from user_cars table
    }

    [System.Serializable]
    public class UserCarData
    {
        public int id;
        public string user_id;
        public string car_id;
        public int is_activated;
        public string purchase_date;
    }

    [System.Serializable]
    public class VehicleSetting
    {
        public string name = "Vehicle 1";
        public string id;
        public int price;
        public GameObject vehicle;
        public Material bodyMat, bodyPartsMat; // Changed from ringMat, smokeMat to bodyMat, bodyPartsMat
        public VehiclePower vehiclePower;
        [HideInInspector]
        public bool Bought = false;

        [System.Serializable]
        public class VehiclePower
        {
            public ObscuredFloat speed;
            public ObscuredFloat braking;
            public ObscuredFloat nitro;
            public ObscuredFloat RPM;
        }
    }

    [System.Serializable]
    public class UserDataResponse
    {
        [JsonProperty("status")]
        public string status;

        [JsonProperty("message")]
        public string message;

        [JsonProperty("user")]
        public UserData user;

        [JsonProperty("encryption_key")]
        public string encryption_key;

        [JsonProperty("nonce")]
        public string nonce;

        [JsonProperty("rawResponse")]
        public string rawResponse;

        [JsonProperty("token_expires_in")]
        public int token_expires_in; // 24 hours in seconds

        [JsonProperty("token_type")]
        public string token_type; // "Bearer"
    }

    [System.Serializable]
    public class CarInfo
    {
        public string id;
        public string name;
        public float price;
    }

    [System.Serializable]
    public class CarListResponse
    {
        public string status;
        public string message;
        public CarInfo[] cars;
        public string nonce;
    }

    [System.Serializable]
    private class PurchaseResponse
    {
        public string status;
        public string message;
        public float? balance;
        public object cars_data; // Using object since it could be complex JSON
        public string nonce;
        public string rawResponse;
        public UserData user;
        public float? referral_balance;
        public bool should_retry;
        public string error_code;
    }

    [System.Serializable]
    private class ActivateCarResponse
    {
        public string status;
        public string message;
        public object cars_data; // Using object since it could be complex JSON
        public string nonce;
        public string rawResponse;
        public UserData user;
        public float? balance; // Adding missing balance property to match PurchaseResponse
    }

    #endregion

    #region Initialization

    private void Awake()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
            Debug.Log("SessionManager instantiated from PurchaseCar");
        }
    }

    private void Start()
    {
        InitializeComponents();
        SetupEventListeners();
        InitializeVehicles();
        FetchInitialData();
    }

    /// <summary>
    /// Initialize and check required components
    /// </summary>
    private void InitializeComponents()
    {
        // Check UI components
        CheckUIComponents();

        // Check session status
        if (SessionManager.Instance == null)
        {
            Debug.LogWarning("SessionManager is not found! Authentication might not work properly.");
        }
        else
        {
            Debug.Log($"SessionManager found. IsLoggedIn: {SessionManager.Instance.IsLoggedIn}");
        }
    }

    /// <summary>
    /// Set up all event listeners for UI buttons
    /// </summary>
    private void SetupEventListeners()
    {
        // Add listener to purchase button
        if (purchaseButton != null)
            purchaseButton.onClick.AddListener(OnPurchaseButtonClick);

        // Add listener to purchase with referral button
        if (purchaseWithReferralButton != null)
            purchaseWithReferralButton.onClick.AddListener(OnPurchaseWithReferralButtonClick);

        // Add listener to activate button
        if (activateButton != null)
            activateButton.onClick.AddListener(OnActivateButtonClick);

        // Add listeners for vehicle selection buttons
        if (nextVehicleButton != null)
            nextVehicleButton.onClick.AddListener(NextVehicle);

        if (previousVehicleButton != null)
            previousVehicleButton.onClick.AddListener(PreviousVehicle);

        // Add listener to next button
        if (nextButton != null)
            nextButton.onClick.AddListener(OnNextButtonClick);

        // Add listener to refresh button
        if (refreshButton != null)
            refreshButton.onClick.AddListener(OnRefreshButtonClick);
    }

    /// <summary>
    /// Initialize vehicle data and display
    /// </summary>
    private void InitializeVehicles()
    {
        if (vehicleSetting == null || vehicleSetting.Length == 0)
            return;

        // Initialize vehicle IDs if not set
        for (int i = 0; i < vehicleSetting.Length; i++)
        {
            if (string.IsNullOrEmpty(vehicleSetting[i].id))
            {
                vehicleSetting[i].id = $"Car:{i}";
            }
        }

        // Store original colors before loading saved colors
        StoreOriginalColors();

        // Load saved vehicle colors
        LoadVehicleColors();

        // Set current vehicle
        currentVehicleNumber = PlayerPrefs.GetInt("CurrentVehicle", 0);
        currentVehicleNumber = Mathf.Clamp(currentVehicleNumber, 0, vehicleSetting.Length - 1);
        UpdateVehicleDisplay();

        // Initialize UI panels based on the current vehicle
        UpdatePanelStates();
    }

    /// <summary>
    /// Update panel states based on current vehicle
    /// </summary>
    private void UpdatePanelStates()
    {
        if (BayPanel == null || customizeVehiclePanel == null || currentVehicle == null)
            return;

        // Check if this is a free car based on price
        float carPrice = 0;
        bool isFreeCar = false;

        if (currentVehicle.id != null && carPrices.TryGetValue(currentVehicle.id, out carPrice))
        {
            isFreeCar = carPrice == 0;
        }

        // Check if car is actually owned (including free cars that have been purchased from server)
        bool isActuallyOwned = currentVehicle.Bought;
        if (isFreeCar && currentVehicle.id != null && ownedCars.ContainsKey(currentVehicle.id))
        {
            isActuallyOwned = true;
        }

        bool shouldShowCustomizePanel = isActuallyOwned;

        // Explicitly set panel states
        customizeVehiclePanel.SetActive(shouldShowCustomizePanel);
        BayPanel.SetActive(!shouldShowCustomizePanel);

        Debug.Log($"UpdatePanelStates: isFreeCar={isFreeCar}, price={carPrice}, isActuallyOwned={isActuallyOwned}, shouldShowCustomizePanel={shouldShowCustomizePanel}");
        Debug.Log($"UpdatePanelStates: customizeVehiclePanel.activeSelf={customizeVehiclePanel.activeSelf}, BayPanel.activeSelf={BayPanel.activeSelf}");
    }

    /// <summary>
    /// Fetch initial data from server
    /// </summary>
    private void FetchInitialData()
    {
        // Fetch car list from server
        StartCoroutine(FetchCarListCoroutine());

        // Fetch user data to update the balance display and owned cars
        FetchUserData();
    }

    #endregion

    #region Vehicle Selection Methods

    /// <summary>
    /// Switch to the next vehicle in the list
    /// </summary>
    public void NextVehicle()
    {
        if (vehicleSetting == null || vehicleSetting.Length == 0) return;

        currentVehicleNumber++;
        currentVehicleNumber = (int)Mathf.Repeat(currentVehicleNumber, vehicleSetting.Length);

        UpdateVehicleDisplay();

        // Check car status with server
        CheckCarStatus();
    }

    /// <summary>
    /// Switch to the previous vehicle in the list
    /// </summary>
    public void PreviousVehicle()
    {
        if (vehicleSetting == null || vehicleSetting.Length == 0) return;

        currentVehicleNumber--;
        currentVehicleNumber = (int)Mathf.Repeat(currentVehicleNumber, vehicleSetting.Length);

        UpdateVehicleDisplay();

        // Check car status with server
        CheckCarStatus();
    }

    /// <summary>
    /// Update the vehicle display to show the currently selected vehicle
    /// </summary>
    private void UpdateVehicleDisplay()
    {
        if (vehicleSetting == null || vehicleSetting.Length == 0) return;

        // Activate the current vehicle and deactivate others
        ActivateCurrentVehicle();

        // Update UI elements
        UpdateVehicleUI();

        // Update panel states based on vehicle ownership
        UpdatePanelStates();
    }

    /// <summary>
    /// Activate the current vehicle and deactivate others
    /// </summary>
    private void ActivateCurrentVehicle()
    {
        foreach (VehicleSetting vSetting in vehicleSetting)
        {
            // Make sure each vehicle has an ID
            if (string.IsNullOrEmpty(vSetting.id))
            {
                int index = Array.IndexOf(vehicleSetting, vSetting);
                vSetting.id = $"Car:{index}";
            }

            if (vSetting == vehicleSetting[currentVehicleNumber])
            {
                if (vSetting.vehicle != null)
                {
                    vSetting.vehicle.SetActive(true);

                    // Find and update CameraRotator if it exists
                    CameraRotator cameraRotator = FindObjectOfType<CameraRotator>();
                    if (cameraRotator != null)
                    {
                        cameraRotator.SetTargetObject(vSetting.vehicle.transform);
                    }
                }
                currentVehicle = vSetting;
            }
            else
            {
                if (vSetting.vehicle != null)
                    vSetting.vehicle.SetActive(false);
            }
        }
    }

    /// <summary>
    /// Update the UI elements for the current vehicle
    /// </summary>
    private void UpdateVehicleUI()
    {
        if (currentVehicle == null) return;

        // Check if this car has zero price (free car)
        bool isFreeCar = IsFreeCar(currentVehicle.id, out float carPrice);

        Debug.Log($"UpdateVehicleUI: currentVehicle.id={currentVehicle.id}, carPrice={carPrice}, isFreeCar={isFreeCar}");

        UpdateVehicleNameAndPrice(isFreeCar, carPrice);
        UpdateVehicleStatsSliders();
        UpdateVehicleOwnershipStatus(isFreeCar);
    }

    /// <summary>
    /// Check if a car is free based on its price
    /// </summary>
    private bool IsFreeCar(string carId, out float price)
    {
        price = 0;
        if (string.IsNullOrEmpty(carId))
            return false;

        if (carPrices.TryGetValue(carId, out price))
            return price == 0;

        return false;
    }

    /// <summary>
    /// Update the vehicle name and price display
    /// </summary>
    private void UpdateVehicleNameAndPrice(bool isFreeCar, float carPrice)
    {
        // Update vehicle name
        if (vehicleNameText != null)
            vehicleNameText.text = currentVehicle.name;

        // Update price text - using priceText instead of vehiclePriceText to match with server prices
        if (priceText != null)
        {
            if (currentVehicle.Bought)
                priceText.text = "Already Owned";
            else if (isFreeCar)
                priceText.text = "FREE";
            else
                priceText.text = $"Price: ${carPrice}";
        }
    }

    /// <summary>
    /// Update the vehicle stats sliders
    /// </summary>
    private void UpdateVehicleStatsSliders()
    {
        if (currentVehicle.vehiclePower == null)
            return;

        // Update vehicle stats sliders
        if (vehicleSpeedSlider != null)
            vehicleSpeedSlider.value = currentVehicle.vehiclePower.speed / 100.0f;

        if (vehicleBrakingSlider != null)
            vehicleBrakingSlider.value = currentVehicle.vehiclePower.braking / 100.0f;

        if (vehicleNitroSlider != null)
            vehicleNitroSlider.value = currentVehicle.vehiclePower.nitro / 100.0f;

        if (vehicleRpmSlider != null)
            vehicleRpmSlider.value = currentVehicle.vehiclePower.RPM / 100.0f;
    }

    /// <summary>
    /// Update the vehicle ownership status and button states
    /// </summary>
    private void UpdateVehicleOwnershipStatus(bool isFreeCar)
    {
        string carId = currentVehicle.id;
        bool isOwned = currentVehicle.Bought;
        bool isActivated = false;

        // Check if the car is in the ownedCars dictionary
        if (!string.IsNullOrEmpty(carId) && ownedCars.TryGetValue(carId, out isActivated))
        {
            isOwned = true;
            currentVehicle.Bought = true;
        }

        // For free cars, only treat them as owned if they exist in server data
        // They need to be purchased (for free) through the server first
        if (isFreeCar)
        {
            if (ownedCars.ContainsKey(carId))
            {
                // Free car is owned through server
                isOwned = true;
                isActivated = ownedCars[carId];
                Debug.Log($"Free car {carId} found in server data: owned={isOwned}, activated={isActivated}");
            }
            else
            {
                // Free car not yet purchased from server
                isOwned = false;
                isActivated = false;
                Debug.Log($"Free car {carId} not yet purchased from server");
            }
        }

        // Update button states
        UpdateButtonStates(carId, isOwned, isActivated);

        // Save current vehicle selection
        if (currentVehicle.Bought)
            PlayerPrefs.SetInt("CurrentVehicle", currentVehicleNumber);
    }

    #endregion

    private void CheckUIComponents()
    {
        // Log UI component status for debugging
        if (priceText == null) Debug.LogError("priceText is not assigned in the Inspector!");
        if (statusText == null) Debug.LogError("statusText is not assigned in the Inspector!");
        if (balanceText == null) Debug.LogError("balanceText is not assigned in the Inspector!");
        if (ownedCarsText == null) Debug.LogError("ownedCarsText is not assigned in the Inspector!");
        if (purchaseButton == null) Debug.LogError("purchaseButton is not assigned in the Inspector!");
        if (activateButton == null) Debug.LogError("activateButton is not assigned in the Inspector!");
        if (nextButton == null) Debug.LogError("nextButton is not assigned in the Inspector!");
        if (refreshButton == null) Debug.LogWarning("refreshButton is not assigned in the Inspector!");
        if (purchaseWithReferralButton == null) Debug.LogWarning("purchaseWithReferralButton is not assigned in the Inspector!");
        if (referralBalanceText == null) Debug.LogWarning("referralBalanceText is not assigned in the Inspector!");

        // Check vehicle selection UI components
        if (vehicleSetting == null || vehicleSetting.Length == 0) Debug.LogError("vehicleSetting array is not assigned or empty!");
        if (nextVehicleButton == null) Debug.LogWarning("nextVehicleButton is not assigned in the Inspector!");
        if (previousVehicleButton == null) Debug.LogWarning("previousVehicleButton is not assigned in the Inspector!");
        if (vehicleSpeedSlider == null) Debug.LogWarning("vehicleSpeedSlider is not assigned in the Inspector!");
        if (vehicleBrakingSlider == null) Debug.LogWarning("vehicleBrakingSlider is not assigned in the Inspector!");
        if (vehicleNitroSlider == null) Debug.LogWarning("vehicleNitroSlider is not assigned in the Inspector!");
        if (vehicleRpmSlider == null) Debug.LogWarning("vehicleRpmSlider is not assigned in the Inspector!");
        if (vehicleNameText == null) Debug.LogWarning("vehicleNameText is not assigned in the Inspector!");
        if (customizeVehiclePanel == null) Debug.LogWarning("customizeVehiclePanel is not assigned in the Inspector!");
        if (BayPanel == null) Debug.LogWarning("BayPanel is not assigned in the Inspector!");

        Debug.Log("PurchaseCar UI components check: " +
                 $"priceText: {(priceText != null ? "OK" : "NULL")}, " +
                 $"statusText: {(statusText != null ? "OK" : "NULL")}, " +
                 $"balanceText: {(balanceText != null ? "OK" : "NULL")}, " +
                 $"ownedCarsText: {(ownedCarsText != null ? "OK" : "NULL")}, " +
                 $"purchaseButton: {(purchaseButton != null ? "OK" : "NULL")}, " +
                 $"activateButton: {(activateButton != null ? "OK" : "NULL")}, " +
                 $"nextButton: {(nextButton != null ? "OK" : "NULL")}, " +
                 $"purchaseWithReferralButton: {(purchaseWithReferralButton != null ? "OK" : "NULL")}, " +
                 $"referralBalanceText: {(referralBalanceText != null ? "OK" : "NULL")}");

        Debug.Log("Vehicle Selection UI components check: " +
                 $"vehicleSetting: {(vehicleSetting != null && vehicleSetting.Length > 0 ? "OK" : "NULL")}, " +
                 $"nextVehicleButton: {(nextVehicleButton != null ? "OK" : "NULL")}, " +
                 $"previousVehicleButton: {(previousVehicleButton != null ? "OK" : "NULL")}, " +
                 $"vehicleSpeedSlider: {(vehicleSpeedSlider != null ? "OK" : "NULL")}, " +
                 $"vehicleBrakingSlider: {(vehicleBrakingSlider != null ? "OK" : "NULL")}, " +
                 $"vehicleNitroSlider: {(vehicleNitroSlider != null ? "OK" : "NULL")}, " +
                 $"vehicleRpmSlider: {(vehicleRpmSlider != null ? "OK" : "NULL")}, " +
                 $"vehicleNameText: {(vehicleNameText != null ? "OK" : "NULL")}, " +
                 $"customizeVehiclePanel: {(customizeVehiclePanel != null ? "OK" : "NULL")}");
    }

    // These methods have been removed as they are no longer needed with the vehicle selection system

    /// <summary>
    /// Update button states based on vehicle ownership status
    /// </summary>
    private void UpdateButtonStates(string carId, bool isOwned, bool isActivated)
    {
        // Check if this is a free car based on price
        bool isFreeCar = IsFreeCar(carId, out float carPrice);

        UpdatePurchaseButtons(carId, isOwned, isActivated, isFreeCar, carPrice);
        UpdateNavigationButtons(isOwned, isActivated, isFreeCar);

        Debug.Log($"[PurchaseCar] Button states for {carId}: isFreeCar={isFreeCar}, price={carPrice}, isOwned={isOwned}, isActivated={isActivated}");
        Debug.Log($"[PurchaseCar] Button interactable states: Purchase={!isOwned}, Activate={isOwned && !isActivated}, Next={isOwned && isActivated}");

        debug_log($"Updated button states for car {carId}: PurchaseButton={!isOwned}, ActivateButton={isOwned && !isActivated}, NextButton={isOwned && isActivated}, isFreeCar={isFreeCar}, price={carPrice}");
    }

    /// <summary>
    /// Update purchase-related buttons
    /// </summary>
    private void UpdatePurchaseButtons(string carId, bool isOwned, bool isActivated, bool isFreeCar, float carPrice)
    {
        // Update regular purchase button - enable for free cars too if not owned
        if (purchaseButton != null)
            purchaseButton.interactable = !isOwned;

        // Update purchase with referral button - for free cars, disable referral purchase since it's already free
        if (purchaseWithReferralButton != null)
        {
            float referralBalance = GetReferralBalance();
            purchaseWithReferralButton.interactable = !isOwned && !isFreeCar && referralBalance >= carPrice;
        }
    }

    /// <summary>
    /// Get the current referral balance from the UI
    /// </summary>
    private float GetReferralBalance()
    {
        float referralBalance = 0;

        if (referralBalanceText != null)
        {
            string balanceText = referralBalanceText.text;
            if (balanceText.Contains("$"))
            {
                string[] parts = balanceText.Split('$');
                if (parts.Length > 1)
                {
                    float.TryParse(parts[1].Trim(), out referralBalance);
                }
            }
        }

        return referralBalance;
    }

    /// <summary>
    /// Update navigation buttons
    /// </summary>
    private void UpdateNavigationButtons(bool isOwned, bool isActivated, bool isFreeCar)
    {
        // Update activate button - enable for all owned but not activated cars (including free cars after purchase)
        if (activateButton != null)
            activateButton.interactable = isOwned && !isActivated;

        // Update next button - only enable if car is owned and activated (including free cars after purchase)
        if (nextButton != null)
            nextButton.interactable = isOwned && isActivated;
    }

    // Fetch user data to display balance and owned cars
    public void FetchUserData()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Fetching user data...";
        }
        StartCoroutine(GetUserDataCoroutine());
    }

    private IEnumerator GetUserDataCoroutine()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        string token, key1;
        bool isAuthenticated = false;

        Debug.Log("Starting user data fetch request with 24-hour token system");

        // Get authentication data from SessionManager if available, otherwise fallback to static properties
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            token = SessionManager.Instance.JWT_Token;
            key1 = SessionManager.Instance.Key1;
            isAuthenticated = true;
            Debug.Log($"Using SessionManager authentication data. Key1: {key1.Substring(0, Math.Min(5, key1.Length))}...");
        }
        else if (!string.IsNullOrEmpty(Login.JWT_Token) && !string.IsNullOrEmpty(Login.Key1))
        {
            token = Login.JWT_Token;
            key1 = Login.Key1;
            isAuthenticated = true;
            Debug.Log($"Using Login static authentication data. Key1: {key1.Substring(0, Math.Min(5, key1.Length))}...");
        }
        else
        {
            Debug.LogWarning("No authentication data found");
            if (statusText != null) statusText.text = "Not logged in";
            if (balanceText != null) balanceText.text = "N/A";
            yield break;
        }

        // Check if we have authentication data
        if (!isAuthenticated)
        {
            Debug.LogWarning("Not logged in or missing credentials");
            if (statusText != null) statusText.text = "Not logged in";
            if (balanceText != null) balanceText.text = "N/A";
            yield break;
        }

        // First try to get encryption key from secure player prefs
        if (string.IsNullOrEmpty(encryptionKey))
        {
            string storedKey = PlayerPrefs.GetString("CarEncryptionKey", "");
            if (!string.IsNullOrEmpty(storedKey))
            {
                debug_log("Retrieved encryption key from PlayerPrefs");
                encryptionKey = storedKey;
            }
        }

        // Create web request - with 24-hour token system, we don't need nonce
        string requestUrl = $"{userDataApiUrl}?key1={UnityWebRequest.EscapeURL(key1)}";
        Debug.Log($"Sending request to: {requestUrl} (Using 24-hour token system)");

        UnityWebRequest request = UnityWebRequest.Get(requestUrl);
        request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        request.SetRequestHeader("Authorization", "Bearer " + token);

        // Update status
        if (statusText != null)
        {
            statusText.text = "Fetching user data...";
        }

        yield return request.SendWebRequest();

        // Handle response
        if (request.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError($"User data fetch error: {request.error} - HTTP Status: {request.responseCode}");

            // Minimal error logging to avoid sensitive data exposure
            string errorDetails = "";
            bool retry = false;
            if (!string.IsNullOrEmpty(request.downloadHandler.text))
            {
                errorDetails = request.downloadHandler.text;

                // Check for nonce error
                if (errorDetails.Contains("Invalid or used nonce") && SessionManager.Instance != null)
                {
                    Debug.LogWarning("Nonce is invalid or used. Will attempt to retry with a fresh nonce on next request.");
                    retry = true;
                }

                // Check for should_retry flag in response
                try
                {
                    var errorJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(request.downloadHandler.text);
                    if (errorJson != null && errorJson.ContainsKey("should_retry"))
                    {
                        if (errorJson["should_retry"] is bool retryValue)
                        {
                            retry = retryValue;
                            Debug.Log($"Server indicated should_retry: {retry}");
                        }
                        else if (errorJson["should_retry"] != null)
                        {
                            string retryStr = errorJson["should_retry"].ToString().ToLower();
                            retry = retryStr == "true" || retryStr == "1";
                            Debug.Log($"Converted should_retry string '{retryStr}' to bool: {retry}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"Error parsing should_retry: {ex.Message}");
                }
            }

            DisplayErrorMessage("Request failed. Please try again.");

            if (retry && SessionManager.Instance.IsLoggedIn)
            {
                Debug.Log("Retrying user data fetch with 24-hour token system.");

                // Check if we have a new nonce in the error response
                try
                {
                    var errorJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(request.downloadHandler.text);
                    if (errorJson != null && errorJson.ContainsKey("nonce") && errorJson["nonce"] != null)
                    {
                        string newNonce = errorJson["nonce"].ToString();
                        if (!string.IsNullOrEmpty(newNonce))
                        {
                            Debug.Log($"Found new nonce in error response: {newNonce}");
                            SessionManager.Instance.UpdateNonce(newNonce);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"Error parsing nonce from error response: {ex.Message}");
                }

                yield return StartCoroutine(GetUserDataCoroutine());
            }
            yield break;
        }

        try
        {
            // Parse response
            string responseText = request.downloadHandler.text;
            Debug.Log($"Raw user data response: {responseText}");

            // Process the user data response
            ProcessUserDataResponse(responseText);
        }
        catch (Exception ex)
        {
            Debug.LogError("Exception in GetUserDataCoroutine: " + ex.Message);
            DisplayErrorMessage("Error: Unable to process data");
        }
    }

    private void ProcessUserDataResponse(string responseText)
    {
        try
        {
            Debug.Log($"Processing user data response: {responseText}");

            // Parse the JSON response
            string cleanJson = FilterOutPhpWarnings(responseText);
            Debug.Log($"Clean JSON after filtering warnings: {cleanJson}");

            cleanJson = EnsureJsonWrapper(cleanJson);
            Debug.Log($"Final JSON to parse: {cleanJson}");

            // Use Newtonsoft.Json instead of JsonUtility
            UserDataResponse response = JsonConvert.DeserializeObject<UserDataResponse>(cleanJson);

            if (response != null && response.user != null)
            {
                Debug.Log($"Parsed user data - Balance: {response.user.balance}, Referral Balance: {response.user.referral_balance}");

                // Update balance display
                if (balanceText != null)
                {
                    float balance = response.user.balance.GetValueOrDefault();
                    balanceText.text = $"{balance:F2}";
                    Debug.Log($"Set balance display to: {balance:F2}");
                }

                // Update referral balance
                if (referralBalanceText != null)
                {
                    float referralBalance = response.user.referral_balance.GetValueOrDefault();
                    referralBalanceText.text = $"Referral Balance: ${referralBalance:F2}";
                    Debug.Log($"Set referral balance display to: ${referralBalance:F2}");

                    // Enable or disable purchase with referral button based on balance
                    if (purchaseWithReferralButton != null && vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
                    {
                        string selectedCarId = vehicleSetting[currentVehicleNumber].id;
                        if (string.IsNullOrEmpty(selectedCarId))
                        {
                            selectedCarId = $"Car:{currentVehicleNumber}";
                        }

                        if (carPrices.TryGetValue(selectedCarId, out float selectedCarPrice))
                        {
                            purchaseWithReferralButton.interactable = referralBalance >= selectedCarPrice;
                            Debug.Log($"Purchase with referral button {(purchaseWithReferralButton.interactable ? "enabled" : "disabled")} for car price: {selectedCarPrice:F2}");
                        }
                    }
                }
            }
            else
            {
                Debug.LogError("Response or user data is null after parsing");
                if (balanceText != null) balanceText.text = "0.00";
                if (referralBalanceText != null) referralBalanceText.text = "Referral Balance: $0.00";
            }

            // Store the encryption key
            if (!string.IsNullOrEmpty(response.encryption_key))
            {
                encryptionKey = response.encryption_key;
                // Debug.Log("Encryption key received: " + encryptionKey);
            }

            // Store the new nonce if provided (with 24-hour token system, nonce is optional)
            if (!string.IsNullOrEmpty(response.nonce))
            {
                PlayerPrefs.SetString("Nonce", response.nonce);
                Debug.Log("New nonce stored: " + response.nonce);

                // Update SessionManager with the new nonce
                if (SessionManager.Instance != null)
                {
                    SessionManager.Instance.UpdateNonce(response.nonce);
                    Debug.Log("SessionManager nonce updated");
                }
            }
            else
            {
                Debug.Log("No nonce in response - using 24-hour token system");
            }

            // Update owned cars
                ownedCars.Clear();

            // Process owned cars from both old and new format
            if (response.user != null)
            {
                if (response.user.cars_data != null)
                {
                    // Old format: string array like ["Car:1 1", "Car:2 0"]
                    foreach (string carData in response.user.cars_data)
                    {
                        string[] parts = carData.Split(' ');
                        if (parts.Length >= 2)
                        {
                            string carId = parts[0];
                            bool isActivated = parts[1] == "1";
                        ownedCars[carId] = isActivated;
                        }
                    }
                }

                if (response.user.user_cars != null)
                {
                    // New format: array of objects
                    foreach (UserCarData car in response.user.user_cars)
                    {
                        if (!string.IsNullOrEmpty(car.car_id))
                        {
                            bool isActivated = car.is_activated == 1;
                            ownedCars[car.car_id] = isActivated;
                        }
                    }
                }
            }

            // Update UI
            UpdateOwnedCarsText();

            // Update vehicle UI if we have a current vehicle
            if (currentVehicle != null)
            {
                UpdateVehicleUI();
            }

            // Set status
            DisplaySuccessMessage("User data updated successfully");
        }
        catch (Exception e)
        {
            Debug.LogError("Error processing user data response: " + e.Message);
            DisplayErrorMessage("Error processing user data");
        }
    }

    private string EnsureJsonWrapper(string json)
    {
        if (string.IsNullOrEmpty(json))
            return "{}";

        // Find where the actual JSON starts (after any PHP notices/warnings)
        int jsonStart = json.IndexOf('{');
        if (jsonStart > 0)
        {
            // Log the header part that we're removing
            string header = json.Substring(0, jsonStart);
            Debug.LogWarning($"Removing non-JSON header: {header}");

            // Extract just the JSON part
            json = json.Substring(jsonStart);
        }

        // If it's not a valid JSON object (doesn't start with {), wrap it
        if (!json.StartsWith("{"))
        {
            string escapedJson = json.Replace("\"", "\\\"");
            json = "{\"rawResponse\":\"" + escapedJson + "\"}";
            Debug.Log("Wrapped non-JSON response: " + json);
        }

        return json;
    }

    private string FilterOutPhpWarnings(string response)
    {
        if (string.IsNullOrEmpty(response))
            return response;

        // جداسازی هشدارهای PHP که معمولاً با <br> یا خط جدید از پاسخ JSON اصلی جدا می‌شوند
        int jsonStart = response.IndexOf("{");
        if (jsonStart > 0)
        {
            string warnings = response.Substring(0, jsonStart);
            debug_log($"PHP warnings filtered: {warnings}");
            return response.Substring(jsonStart);
        }

        return response;
    }

    private void UpdateOwnedCarsText()
    {
        if (ownedCarsText != null)
        {
            ownedCarsText.text = "Owned Cars:";
            foreach (var car in ownedCars)
            {
                string carId = car.Key;
                bool isActivated = car.Value;

                // Get car name if available
                string carName = carNames.TryGetValue(carId, out string name) ? name : carId;

                // Check if this is a free car
                bool isFreeCar = false;
                if (carPrices.TryGetValue(carId, out float price))
                {
                    isFreeCar = price == 0;
                }

                // Format display text
                string displayText;
                if (isFreeCar)
                {
                    displayText = "Free Car - " + (isActivated ? "Ready to use" : "Available");
                }
                else
                {
                    displayText = carName + " - " + (isActivated ? "Activated" : "Not Activated");
                }

                ownedCarsText.text += "\n" + displayText;
            }
        }
        else
        {
            Debug.LogWarning("ownedCarsText is null");
        }
    }

    private void OnPurchaseButtonClick()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Starting purchase...";
        }

        // Get the car ID from the vehicle selection system
        string carId;
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            // Use the vehicle selection system
            if (!string.IsNullOrEmpty(vehicleSetting[currentVehicleNumber].id))
            {
                carId = vehicleSetting[currentVehicleNumber].id;
            }
            else
            {
                carId = $"Car:{currentVehicleNumber}";
            }

            // Check if this is a free car based on price
            float carPrice = 0;
            bool isFreeCar = false;

            if (!string.IsNullOrEmpty(carId) && carPrices.TryGetValue(carId, out carPrice))
            {
                isFreeCar = carPrice == 0;
            }

            if (isFreeCar)
            {
                // For free cars, show appropriate message
                Debug.Log($"Free car {carId} detected. Processing free purchase...");
                if (statusText != null) statusText.text = "Getting your free car...";

                // Continue with the normal purchase process - server will handle it as free
            }
        }
        else
        {
            DisplayErrorMessage("Error: No vehicle selected");
            return;
        }

        debug_log($"===== PURCHASE ATTEMPT =====");
        debug_log($"Car ID: {carId}");
        debug_log($"Is authenticated: {(SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)}");

        // Check authentication data in more detail
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            debug_log($"Token length: {SessionManager.Instance.JWT_Token?.Length ?? 0}");
            debug_log($"Key1 length: {SessionManager.Instance.Key1?.Length ?? 0}");
            debug_log($"Current nonce: {SessionManager.Instance.CurrentNonce}");
        }
        else if (!string.IsNullOrEmpty(Login.JWT_Token) && !string.IsNullOrEmpty(Login.Key1) && !string.IsNullOrEmpty(Login.CurrentNonce))
        {
            debug_log($"Using static Login data");
            debug_log($"Token length: {Login.JWT_Token?.Length ?? 0}");
            debug_log($"Key1 length: {Login.Key1?.Length ?? 0}");
            debug_log($"Current nonce: {Login.CurrentNonce}");
        }
        else
        {
            debug_log("NO AUTHENTICATION DATA AVAILABLE - Login will fail");
            if (statusText != null) statusText.text = "Error: Not logged in";
            return;
        }

        // Check if car is already owned
        if (ownedCars.TryGetValue(carId, out bool isActivated))
        {
            if (statusText != null) statusText.text = "You already own this car";
            debug_log($"Purchase canceled - Car already owned: {carId}, Activated: {isActivated}");
            return;
        }

        // If using the vehicle selection system, check if the current vehicle is already bought
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            if (vehicleSetting[currentVehicleNumber].Bought)
            {
                // This is just informational, not an error
                ResetStatusTextColor();
                if (statusText != null) statusText.text = "You already own this car";
                debug_log($"Purchase canceled - Car already owned: {carId}");
                return;
            }
        }

        debug_log($"Starting API request for car purchase: {carId}");
        StartCoroutine(ApiRequestCoroutine(ApiOperation.PurchaseCar, carId));
    }

    public void OnActivateButtonClick()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Starting activation...";
        }

        // Get the car ID from the vehicle selection system
        string carId;
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            // Use the vehicle selection system
            if (!string.IsNullOrEmpty(vehicleSetting[currentVehicleNumber].id))
            {
                carId = vehicleSetting[currentVehicleNumber].id;
            }
            else
            {
                carId = $"Car:{currentVehicleNumber}";
            }

            // Check if the current vehicle is bought
            if (!vehicleSetting[currentVehicleNumber].Bought)
            {
                // This is just informational, not an error
                ResetStatusTextColor();
                if (statusText != null) statusText.text = "You need to purchase this car first";
                return;
            }
        }
        else
        {
            DisplayErrorMessage("Error: No vehicle selected");
            return;
        }

        // Check if car is owned but not activated
        if (ownedCars.TryGetValue(carId, out bool isActivated))
        {
            if (isActivated)
            {
                // Get car name if available
                string carName = carNames.TryGetValue(carId, out string name) ? name : "This car";
                if (statusText != null) statusText.text = $"{carName} is already activated";
                return;
            }

            // Car is owned but not activated, so activate it
            StartCoroutine(ApiRequestCoroutine(ApiOperation.ActivateCar, carId));
        }
        else
        {
            // Check if this is a free car
            bool isFreeCar = false;
            if (carPrices.TryGetValue(carId, out float price))
            {
                isFreeCar = price == 0;
            }

            if (isFreeCar)
            {
                if (statusText != null) statusText.text = "This car is free! Please use the Purchase button to get it.";
                Debug.Log($"Free car {carId} detected. User should use Purchase button to get it first.");
            }
            else
            {
                if (statusText != null) statusText.text = "You need to purchase this car first";
            }
        }
    }

    // These classes are already defined in the Data Models region

    private string GetStringValueFromJson(string json, string key)
    {
        try
        {
            int keyIndex = json.IndexOf("\"" + key + "\":\"");
            if (keyIndex >= 0)
            {
                int startIndex = keyIndex + key.Length + 4; // 4 for ":"" chars
                int endIndex = json.IndexOf("\"", startIndex);
                if (endIndex > startIndex)
                {
                    return json.Substring(startIndex, endIndex - startIndex);
                }
            }
            return null;
        }
        catch
        {
            return null;
        }
    }

    // Add button to test API connectivity
    public void TestApiConnection()
    {
        StartCoroutine(TestApiCoroutine());
    }

    private IEnumerator TestApiCoroutine()
    {
        if (statusText != null)
        {
            statusText.text = "Testing API connection...";
        }

        // Test URL includes ?test=1 to trigger the test endpoint
        string testUrl = apiUrl + "?test=1";
        UnityWebRequest request = UnityWebRequest.Get(testUrl);

        Debug.Log($"Sending test request to: {testUrl}");
        yield return request.SendWebRequest();

        if (request.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError($"API Test Error: {request.error}");
            if (statusText != null) statusText.text = $"API test failed: {request.error}";
            yield break;
        }

        string responseText = request.downloadHandler.text;
        Debug.Log($"API test response: {responseText}");

        if (string.IsNullOrEmpty(responseText))
        {
            Debug.LogError("Empty response from API test");
            if (statusText != null) statusText.text = "API test returned empty response";
            yield break;
        }

        if (statusText != null)
        {
            statusText.text = "API test successful!";
        }

        // Now try the FetchUserData operation
        StartCoroutine(ApiRequestCoroutine(ApiOperation.FetchUserData));
    }

    // Helper method to display error messages in red color
    private void DisplayErrorMessage(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
            statusText.color = Color.red;
        }
    }

    // Helper method to display success messages in green color
    private void DisplaySuccessMessage(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
            statusText.color = Color.green;
        }
    }

    // Helper method to reset status text color to default
    private void ResetStatusTextColor()
    {
        if (statusText != null)
        {
            statusText.color = Color.white; // Reset to default color
        }
    }

    // Helper method for 24-hour token system - no longer needs to fetch nonce
    private IEnumerator FetchUserDataForNonce()
    {
        Debug.Log("Using 24-hour token system - no need to fetch fresh nonce");

        if (SessionManager.Instance == null || !SessionManager.Instance.IsLoggedIn)
        {
            Debug.LogError("Cannot proceed: Not logged in");
            yield break;
        }

        // With 24-hour token system, we don't need to fetch a new nonce for each request
        // This method is kept for compatibility with existing code

        // Check if we have a valid token
        string token = SessionManager.Instance.JWT_Token;
        string key1 = SessionManager.Instance.Key1;

        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1))
        {
            Debug.LogError("Missing authentication data (token or key1)");
            yield break;
        }

        // Log that we're using the 24-hour token system
        Debug.Log("Using 24-hour JWT token authentication system");
        Debug.Log($"Current token length: {token.Length}");
        Debug.Log($"Current key1 length: {key1.Length}");

        // With 24-hour token system, we don't need nonce at all
        Debug.Log("With 24-hour token system, nonce is not required for authentication");

        // If we still need a nonce for backward compatibility, we can create a dummy one
        if (string.IsNullOrEmpty(SessionManager.Instance.CurrentNonce))
        {
            string dummyNonce = System.Guid.NewGuid().ToString("N");
            SessionManager.Instance.UpdateNonce(dummyNonce);
            Debug.Log($"Created dummy nonce for backward compatibility: {dummyNonce}");
        }
        else
        {
            Debug.Log($"Using existing nonce: {SessionManager.Instance.CurrentNonce}");
        }
    }

    private IEnumerator ApiRequestCoroutine(ApiOperation operation, string targetCarId = null, bool useReferralBalance = false)
    {
        // Reset status text color to default at the beginning of API request
        ResetStatusTextColor();

        if (SessionManager.Instance == null)
        {
            Debug.LogError("SessionManager is not available. Cannot make API requests.");
            DisplayErrorMessage("Error: Session Manager not found");
            yield break;
        }

        if (!SessionManager.Instance.IsLoggedIn)
        {
            Debug.LogError("User is not logged in. Cannot make API requests.");
            DisplayErrorMessage("Error: Not logged in");
            yield break;
        }

        // With 24-hour token system, we don't need nonce at all
        Debug.Log("Using 24-hour token system - nonce is not required");

        // Get auth token and key1
        string token = SessionManager.Instance.JWT_Token;
        string key1 = SessionManager.Instance.Key1;

        // For backward compatibility, we'll still use nonce if available
        string nonce = SessionManager.Instance.CurrentNonce;

        Debug.Log($"Using JWT token (length: {token?.Length ?? 0})");

        // With 24-hour token system, only token and key1 are required
        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1))
        {
            Debug.LogError("Missing authentication credentials. Cannot make API requests.");
            statusText.text = "Error: Missing authentication";
            yield break;
        }

        // With 24-hour token system, nonce is optional
        if (string.IsNullOrEmpty(nonce))
        {
            Debug.Log("No nonce available, but proceeding with 24-hour token system");
        }

        // Select appropriate API URL and operation name based on operation type
        string apiEndpoint = "";
        string operationName = "";

        switch (operation)
        {
            case ApiOperation.FetchUserData:
                apiEndpoint = userDataApiUrl;
                operationName = "fetch_user_data";
                break;

            case ApiOperation.PurchaseCar:
            case ApiOperation.ActivateCar:
            case ApiOperation.GetAllCars:
                apiEndpoint = apiUrl;
                operationName = operation.ToString().ToLower();
                break;

            default:
                Debug.LogError("Unknown operation type");
                statusText.text = "Error: Invalid operation";
                yield break;
        }

        bool requestSucceeded = false;
        int maxRetries = 2;
        int currentRetry = 0;

        while (!requestSucceeded && currentRetry <= maxRetries)
        {
            if (currentRetry > 0)
            {
                Debug.Log($"Retry attempt {currentRetry}/{maxRetries}");
                // Get fresh nonce on retry
                nonce = SessionManager.Instance.CurrentNonce;
                Debug.Log($"Using fresh nonce: {nonce}");
            }

            currentRetry++;

            // Create form data
            WWWForm form = new WWWForm();
            form.AddField("key1", key1);
            form.AddField("operation", operationName);

            // With 24-hour token system, nonce is optional
            if (!string.IsNullOrEmpty(nonce))
            {
                form.AddField("nonce", nonce);
                Debug.Log("Including nonce in request");
            }
            else
            {
                Debug.Log("No nonce included in request - using 24-hour token system");
            }

            if (operation == ApiOperation.PurchaseCar || operation == ApiOperation.ActivateCar)
            {
                form.AddField("car_id", targetCarId);

                // اضافه کردن فیلد جدید برای تعیین نوع خرید
                if (operation == ApiOperation.PurchaseCar && useReferralBalance)
                {
                    form.AddField("use_referral_balance", "true");
                }
            }

            // Create request with auth header
            UnityWebRequest www = UnityWebRequest.Post(apiEndpoint, form);
            www.SetRequestHeader("Authorization", "Bearer " + token);

            // Log the request for debugging
            StringBuilder requestLog = new StringBuilder();
            requestLog.AppendLine($"Making API request to {apiEndpoint}");
            requestLog.AppendLine($"Operation: {operationName}");
            requestLog.AppendLine("Parameters:");
            foreach (var field in form.headers)
            {
                if (!IsSecureParameter(field.Key))
                {
                    requestLog.AppendLine($"  {field.Key}: {field.Value}");
                }
                else
                {
                    requestLog.AppendLine($"  {field.Key}: [SECURED]");
                }
            }

            debug_log(requestLog.ToString());

            // Send request
            yield return www.SendWebRequest();

            // Process response
            if (www.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"API Error: {www.error}");

                // Try to parse the error response for nonce update
                if (!string.IsNullOrEmpty(www.downloadHandler.text))
                {
                    Debug.LogError("Response: " + www.downloadHandler.text);

                    // Extract error message and check for nonce error
                    bool isNonceError = false;
                    bool shouldRetry = false;
                    string errorMessage = "Unknown error";
                    string newNonce = null;

                    try
                    {
                        Debug.Log("Error response: " + www.downloadHandler.text);
                        var errorJson = JsonConvert.DeserializeObject<PurchaseResponse>(www.downloadHandler.text);
                        if (errorJson != null)
                        {
                            // Get error message
                            if (!string.IsNullOrEmpty(errorJson.message))
                            {
                                errorMessage = errorJson.message;
                                isNonceError = errorMessage.Contains("Invalid or used nonce");
                                Debug.Log($"Error message: {errorMessage}, isNonceError: {isNonceError}");
                            }

                            // Check if the server indicates we should retry
                            try
                            {
                                // Try to parse the should_retry field
                                var responseDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(www.downloadHandler.text);
                                if (responseDict != null && responseDict.ContainsKey("should_retry"))
                                {
                                    if (responseDict["should_retry"] is bool retryValue)
                                    {
                                        shouldRetry = retryValue;
                                        Debug.Log($"Server indicated should_retry: {shouldRetry}");
                                    }
                                    else if (responseDict["should_retry"] != null)
                                    {
                                        // Try to convert string "true"/"false" to bool
                                        string retryStr = responseDict["should_retry"].ToString().ToLower();
                                        shouldRetry = retryStr == "true" || retryStr == "1";
                                        Debug.Log($"Converted should_retry string '{retryStr}' to bool: {shouldRetry}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Error parsing should_retry: {ex.Message}");
                            }

                            // Update nonce if provided in error response
                            if (!string.IsNullOrEmpty(errorJson.nonce))
                            {
                                newNonce = errorJson.nonce;
                                Debug.Log($"Found new nonce in error response: {newNonce}");

                                // Always update the nonce immediately
                                SessionManager.Instance.UpdateNonce(newNonce);
                                Debug.Log($"Updated SessionManager nonce to: {newNonce}");
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError("Failed to parse error response: " + e.Message);
                    }

                    // Handle nonce error or any error that should be retried
                    if (isNonceError || shouldRetry)
                    {
                        Debug.Log($"Error requires retry. isNonceError={isNonceError}, shouldRetry={shouldRetry}");
                        statusText.text = "Refreshing session...";

                        // With 24-hour token system, we should still handle nonce errors for backward compatibility
                        // If we got a new nonce from the error response, use it
                        if (!string.IsNullOrEmpty(newNonce))
                        {
                            Debug.Log($"Using new nonce from error response: {newNonce}");
                            nonce = newNonce;

                            // Update the nonce in SessionManager
                            SessionManager.Instance.UpdateNonce(newNonce);
                        }
                        else
                        {
                            // With 24-hour token system, we can create a new nonce if needed
                            string dummyNonce = System.Guid.NewGuid().ToString("N");
                            SessionManager.Instance.UpdateNonce(dummyNonce);
                            nonce = dummyNonce;
                            Debug.Log($"Created new nonce for retry: {dummyNonce}");
                        }

                        // Try again in the next iteration
                        www.Dispose();
                        continue;
                    }
                    else
                    {
                        // Non-nonce error, display to user
                        DisplayErrorMessage($"Error: {errorMessage}");
                        www.Dispose();
                        yield break;
                    }
                }
                else
                {
                    // No response body, show generic error
                    statusText.text = $"Error: {www.error}";
                    www.Dispose();
                    yield break;
                }
            }
            else
            {
                // Request was successful
                requestSucceeded = true;
                string responseText = www.downloadHandler.text;

                // Check if it's an encrypted response
                if (IsEncryptedResponse(responseText))
                {
                    Debug.Log("Received encrypted response");

                    // Try to decrypt the response
                    if (!string.IsNullOrEmpty(encryptionKey))
                    {
                        try
                        {
                            responseText = DecryptString(responseText, encryptionKey);
                            Debug.Log("Response decrypted successfully");
                        }
                        catch (Exception e)
                        {
                            Debug.LogError("Failed to decrypt response: " + e.Message);
                            statusText.text = "Error decrypting response";
                            www.Dispose();
                            yield break;
                        }
                    }
                    else
                    {
                        Debug.LogError("Cannot decrypt response: Missing encryption key");
                        statusText.text = "Error: No encryption key";
                        www.Dispose();
                        yield break;
                    }
                }

                // Extract and update nonce from response
                try
                {
                    var baseResponse = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseText);
                    if (baseResponse != null && baseResponse.ContainsKey("nonce") && baseResponse["nonce"] != null)
                    {
                        string newNonce = baseResponse["nonce"].ToString();
                        if (!string.IsNullOrEmpty(newNonce))
                        {
                            Debug.Log($"Updating nonce from successful response: {newNonce}");
                            SessionManager.Instance.UpdateNonce(newNonce);
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Could not extract nonce from response: {e.Message}");
                }

                // Process response based on operation type
                switch (operation)
                {
                    case ApiOperation.FetchUserData:
                        ProcessUserDataResponse(responseText);
                        break;

                    case ApiOperation.PurchaseCar:
                        ProcessPurchaseResponse(responseText, targetCarId);
                        break;

                    case ApiOperation.ActivateCar:
                        ProcessActivationResponse(responseText, targetCarId);
                        break;

                    case ApiOperation.GetAllCars:
                        ProcessCarListResponse(responseText);
                        break;
                }

                www.Dispose();
            }
        }

        if (!requestSucceeded)
        {
            Debug.LogError("Failed to complete API request after maximum retries");
            DisplayErrorMessage("Error: Failed after maximum retries");
        }
    }

    // تشخیص پارامترهای نیازمند رمزنگاری
    private bool IsSecureParameter(string paramName)
    {
        // پارامترهای حساس که باید رمزنگاری شوند
        string[] secureParams = new string[] {
            "jwt", "password", "email", "wallet_address", "card_number",
            "transaction_id", "private_key", "seed_phrase", "api_key"
        };

        return Array.Exists(secureParams, p => p.Equals(paramName, StringComparison.OrdinalIgnoreCase));
    }

    // تشخیص پاسخ رمزنگاری شده
    private bool IsEncryptedResponse(string response)
    {
        if (string.IsNullOrEmpty(response))
            return false;

        // بررسی اگر پاسخ با پیشوند رمزنگاری شده شروع می‌شود
        // می‌توانید علامت یا پیشوند خاصی برای تشخیص پاسخ‌های رمزنگاری شده تعریف کنید
        return response.StartsWith("ENC:") || response.StartsWith("ENCRYPTED:");
    }

    private void debug_log(string message)
    {
        Debug.Log("[PurchaseCar] " + message);
    }

    // متدهای رمزنگاری برای امن‌سازی ارتباطات

    // رمزنگاری یک رشته
    private string EncryptString(string plainText, string key)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText) || string.IsNullOrEmpty(key))
                return plainText;

            // Ensure the key is exactly 32 bytes (256 bits) for AES-256
            // If the key is shorter or longer, adjust it
            byte[] keyBytes;
            if (key.Length * sizeof(char) != 32)
            {
                // Convert to bytes
                byte[] originalKeyBytes = Encoding.UTF8.GetBytes(key);

                // Create a new key of exactly 32 bytes
                keyBytes = new byte[32];

                // Copy as many bytes as possible from the original key
                int bytesToCopy = Math.Min(originalKeyBytes.Length, 32);
                Array.Copy(originalKeyBytes, keyBytes, bytesToCopy);

                // If the key was too short, pad with zeros
                // If it was too long, we've truncated it
                debug_log($"Adjusted key length from {originalKeyBytes.Length} to 32 bytes");
            }
            else
            {
                keyBytes = Encoding.UTF8.GetBytes(key);
            }

            byte[] iv = new byte[16]; // AES block size is always 16 bytes for IV
            byte[] array;

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = iv;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using (var memoryStream = new System.IO.MemoryStream())
                {
                    using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                    {
                        using (var streamWriter = new System.IO.StreamWriter(cryptoStream))
                        {
                            streamWriter.Write(plainText);
                        }
                        array = memoryStream.ToArray();
                    }
                }
            }
            return Convert.ToBase64String(array);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Encryption error: {ex.Message}");
            return "";
        }
    }

    // رمزگشایی یک رشته
    private string DecryptString(string cipherText, string key)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText) || string.IsNullOrEmpty(key))
                return cipherText;

            // Ensure the key is exactly 32 bytes (256 bits) for AES-256
            // If the key is shorter or longer, adjust it
            byte[] keyBytes;
            if (key.Length * sizeof(char) != 32)
            {
                // Convert to bytes
                byte[] originalKeyBytes = Encoding.UTF8.GetBytes(key);

                // Create a new key of exactly 32 bytes
                keyBytes = new byte[32];

                // Copy as many bytes as possible from the original key
                int bytesToCopy = Math.Min(originalKeyBytes.Length, 32);
                Array.Copy(originalKeyBytes, keyBytes, bytesToCopy);

                // If the key was too short, pad with zeros
                // If it was too long, we've truncated it
            }
            else
            {
                keyBytes = Encoding.UTF8.GetBytes(key);
            }

            byte[] iv = new byte[16]; // AES block size is always 16 bytes for IV
            byte[] buffer = Convert.FromBase64String(cipherText);

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = iv;
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                using (var memoryStream = new System.IO.MemoryStream(buffer))
                {
                    using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                    {
                        using (var streamReader = new System.IO.StreamReader(cryptoStream))
                        {
                            return streamReader.ReadToEnd();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Decryption error: {ex.Message}");
            return "";
        }
    }

    // ذخیره‌سازی امن داده در PlayerPrefs
    private void SecurePlayerPrefsSet(string key, string value)
    {
        var encryptedValue = EncryptString(value, encryptionKey);
        PlayerPrefs.SetString(key, encryptedValue);
    }

    // دریافت امن داده از PlayerPrefs
    private string SecurePlayerPrefsGet(string key)
    {
        var encryptedValue = PlayerPrefs.GetString(key, "");
        if (string.IsNullOrEmpty(encryptedValue) || string.IsNullOrEmpty(encryptionKey))
            return "";

        return DecryptString(encryptedValue, encryptionKey);
    }

    // Update vehicle UI
    private void Update()
    {
        // Update vehicle UI if needed
        if (currentVehicle != null)
        {
            UpdateVehicleUI();
        }
    }

    #region Vehicle Customization Methods

    /// <summary>
    /// Apply the selected color to the current vehicle
    /// </summary>
    public void ActiveCurrentColor(Image activeImage)
    {
        if (currentVehicle == null) return;

        mainColor = activeImage.color;

        // Check which color panel is active (body or body parts)
        if (activeImage.CompareTag("BodyColor"))
        {
            ApplyColorToBody(mainColor);
        }
        else if (activeImage.CompareTag("BodyPartsColor"))
        {
            ApplyColorToBodyParts(mainColor);
        }
    }

    /// <summary>
    /// Apply a color to the vehicle body
    /// </summary>
    private void ApplyColorToBody(Color color)
    {
        if (currentVehicle == null || currentVehicle.bodyMat == null) return;

        currentVehicle.bodyMat.SetColor("_Color", color);
        PlayerPrefsX.SetColor("VehicleBodyColor" + currentVehicleNumber, color);
        Debug.Log($"Applied {color} to body material");
    }

    /// <summary>
    /// Apply a color to the vehicle body parts
    /// </summary>
    private void ApplyColorToBodyParts(Color color)
    {
        if (currentVehicle == null || currentVehicle.bodyPartsMat == null) return;

        currentVehicle.bodyPartsMat.SetColor("_Color", color);
        PlayerPrefsX.SetColor("VehicleBodyPartsColor" + currentVehicleNumber, color);
        Debug.Log($"Applied {color} to body parts material");
    }

    /// <summary>
    /// Generate random colors for the vehicle
    /// </summary>
    public void RandomColor()
    {
        if (currentVehicle == null) return;

        randomColorActive = true;

        // Generate random colors for body and body parts
        Color randomBodyColor = new Color(UnityEngine.Random.Range(0.0f, 1.1f), UnityEngine.Random.Range(0.0f, 1.1f), UnityEngine.Random.Range(0.0f, 1.1f));
        Color randomBodyPartsColor = new Color(UnityEngine.Random.Range(0.0f, 1.1f), UnityEngine.Random.Range(0.0f, 1.1f), UnityEngine.Random.Range(0.0f, 1.1f));

        ApplyColorToBody(randomBodyColor);
        ApplyColorToBodyParts(randomBodyPartsColor);
    }

    /// <summary>
    /// Load saved vehicle colors from PlayerPrefs
    /// </summary>
    public void LoadVehicleColors()
    {
        if (vehicleSetting == null) return;

        for (int i = 0; i < vehicleSetting.Length; i++)
        {
            LoadVehicleColorForIndex(i);
        }
    }

    /// <summary>
    /// Load saved colors for a specific vehicle index
    /// </summary>
    private void LoadVehicleColorForIndex(int index)
    {
        if (vehicleSetting == null || index < 0 || index >= vehicleSetting.Length) return;

        // Load body color
        if (vehicleSetting[index].bodyMat != null)
        {
            Color savedBodyColor = PlayerPrefsX.GetColor("VehicleBodyColor" + index);
            if (savedBodyColor != Color.clear) // Only apply if a color was previously saved
            {
                vehicleSetting[index].bodyMat.SetColor("_Color", savedBodyColor);
            }
            // Otherwise keep the default material color
        }

        // Load body parts color
        if (vehicleSetting[index].bodyPartsMat != null)
        {
            Color savedBodyPartsColor = PlayerPrefsX.GetColor("VehicleBodyPartsColor" + index);
            if (savedBodyPartsColor != Color.clear) // Only apply if a color was previously saved
            {
                vehicleSetting[index].bodyPartsMat.SetColor("_Color", savedBodyPartsColor);
            }
            // Otherwise keep the default material color
        }
    }

    #endregion

    #region Color Selection Methods

    /// <summary>
    /// Method to set the current color target
    /// </summary>
    public void SetColorTarget(int target)
    {
        currentColorTarget = (ColorTarget)target;
        Debug.Log($"Color target set to: {currentColorTarget}");
    }

    /// <summary>
    /// Apply white color to the current target
    /// </summary>
    public void SetWhiteColor() => ApplyColorToTarget(Color.white);

    /// <summary>
    /// Apply yellow color to the current target
    /// </summary>
    public void SetYellowColor() => ApplyColorToTarget(Color.yellow);

    /// <summary>
    /// Apply red color to the current target
    /// </summary>
    public void SetRedColor() => ApplyColorToTarget(Color.red);

    /// <summary>
    /// Apply black color to the current target
    /// </summary>
    public void SetBlackColor() => ApplyColorToTarget(Color.black);

    /// <summary>
    /// Apply purple color to the current target
    /// </summary>
    public void SetPurpleColor() => ApplyColorToTarget(new Color(0.5f, 0f, 0.5f));

    /// <summary>
    /// Apply dark brown color to the current target
    /// </summary>
    public void SetDarkBrownColor() => ApplyColorToTarget(new Color(0.4f, 0.2f, 0f));

    /// <summary>
    /// Apply pink color to the current target
    /// </summary>
    public void SetPinkColor() => ApplyColorToTarget(new Color(1f, 0.4f, 0.7f));

    /// <summary>
    /// Apply blue color to the current target
    /// </summary>
    public void SetBlueColor() => ApplyColorToTarget(Color.blue);

    /// <summary>
    /// Store original colors of materials when the game starts
    /// </summary>
    private void StoreOriginalColors()
    {
        if (vehicleSetting == null) return;

        for (int i = 0; i < vehicleSetting.Length; i++)
        {
            StoreOriginalColorsForIndex(i);
        }

        Debug.Log("Original vehicle colors stored");
    }

    /// <summary>
    /// Store original colors for a specific vehicle index
    /// </summary>
    private void StoreOriginalColorsForIndex(int index)
    {
        if (vehicleSetting == null || index < 0 || index >= vehicleSetting.Length) return;

        if (vehicleSetting[index].bodyMat != null)
        {
            originalBodyColors[index] = vehicleSetting[index].bodyMat.GetColor("_Color");
        }

        if (vehicleSetting[index].bodyPartsMat != null)
        {
            originalBodyPartsColors[index] = vehicleSetting[index].bodyPartsMat.GetColor("_Color");
        }
    }

    /// <summary>
    /// Reset colors to default
    /// </summary>
    public void ResetToDefaultColors()
    {
        if (currentVehicle == null) return;

        // Clear saved colors
        PlayerPrefsX.SetColor("VehicleBodyColor" + currentVehicleNumber, Color.clear);
        PlayerPrefsX.SetColor("VehicleBodyPartsColor" + currentVehicleNumber, Color.clear);

        ResetBodyColor();
        ResetBodyPartsColor();

        // Force reload the vehicle to ensure all materials are properly reset
        UpdateVehicleDisplay();

        // Show success message
        DisplaySuccessMessage("Colors reset to default");
    }

    /// <summary>
    /// Reset body color to original or default
    /// </summary>
    private void ResetBodyColor()
    {
        if (currentVehicle == null || currentVehicle.bodyMat == null) return;

        if (originalBodyColors.TryGetValue(currentVehicleNumber, out Color originalColor))
        {
            currentVehicle.bodyMat.SetColor("_Color", originalColor);
            Debug.Log($"Reset body material to original color: {originalColor}");
        }
        else
        {
            // Fallback to a standard color if original wasn't stored
            Color defaultColor = new Color(0.8f, 0.8f, 0.8f, 1.0f);
            currentVehicle.bodyMat.SetColor("_Color", defaultColor);
            Debug.Log($"Reset body material to default color: {defaultColor}");
        }
    }

    /// <summary>
    /// Reset body parts color to original or default
    /// </summary>
    private void ResetBodyPartsColor()
    {
        if (currentVehicle == null || currentVehicle.bodyPartsMat == null) return;

        if (originalBodyPartsColors.TryGetValue(currentVehicleNumber, out Color originalColor))
        {
            currentVehicle.bodyPartsMat.SetColor("_Color", originalColor);
            Debug.Log($"Reset body parts material to original color: {originalColor}");
        }
        else
        {
            // Fallback to a standard color if original wasn't stored
            Color defaultColor = new Color(0.5f, 0.5f, 0.5f, 1.0f);
            currentVehicle.bodyPartsMat.SetColor("_Color", defaultColor);
            Debug.Log($"Reset body parts material to default color: {defaultColor}");
        }
    }

    /// <summary>
    /// Helper method to apply color to the selected target
    /// </summary>
    private void ApplyColorToTarget(Color color)
    {
        if (currentVehicle == null) return;

        switch (currentColorTarget)
        {
            case ColorTarget.Body:
                ApplyColorToBody(color);
                break;

            case ColorTarget.BodyParts:
                ApplyColorToBodyParts(color);
                break;
        }
    }

    #endregion

    #region Response Processing

    /// <summary>
    /// Process the purchase response from the server
    /// </summary>
    private void ProcessPurchaseResponse(string responseText, string carId)
    {
        Debug.Log($"Processing purchase response for car: {carId}");

        try
        {
            // Parse the JSON response
            string cleanJson = FilterOutPhpWarnings(responseText);
            Debug.Log($"Clean JSON for processing: {cleanJson}");

            PurchaseResponse response = JsonConvert.DeserializeObject<PurchaseResponse>(cleanJson);

            // Check if response was successfully parsed
            if (response == null)
            {
                Debug.LogError("Failed to parse purchase response: " + cleanJson);
                DisplayErrorMessage("Error: Failed to parse server response");
                return;
            }

            // Store the new nonce if available
            UpdateNonceFromResponse(response.nonce);

            // Check for errors
            if (response.status != "success")
            {
                Debug.LogError("Purchase error: " + response.message);
                DisplayErrorMessage(response.message);
                return;
            }

            // Update the vehicle selection system
            UpdateVehicleAfterPurchase(carId);

            Debug.Log("Purchase successful! Updating car ownership and balance displays");

            // Update user data based on response format
            if (response.user != null && response.user.user_cars != null)
            {
                UpdateUserDataFromNewFormat(response, carId);
            }
            else
            {
                UpdateUserDataFromOldFormat(response, carId);
            }
        }
        catch (Exception e)
        {
            Debug.LogError("Error processing purchase response: " + e.Message);
            DisplayErrorMessage("Error processing purchase response");
        }
    }

    /// <summary>
    /// Update the nonce from the response
    /// </summary>
    private void UpdateNonceFromResponse(string nonce)
    {
        if (!string.IsNullOrEmpty(nonce) && SessionManager.Instance != null)
        {
            Debug.Log($"Updating nonce from response: {nonce}");
            SessionManager.Instance.UpdateNonce(nonce);
        }
    }

    /// <summary>
    /// Update the vehicle after purchase
    /// </summary>
    private void UpdateVehicleAfterPurchase(string carId)
    {
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            // Mark the current vehicle as bought
            vehicleSetting[currentVehicleNumber].Bought = true;

            // Update the UI
            UpdateVehicleUI();

            // Explicitly set panel states for purchased car
            if (customizeVehiclePanel != null)
            {
                customizeVehiclePanel.SetActive(true);
                Debug.Log($"UpdateVehicleAfterPurchase: Activated customizeVehiclePanel for purchased car {carId}");
            }

            if (BayPanel != null)
            {
                BayPanel.SetActive(false);
                Debug.Log($"UpdateVehicleAfterPurchase: Deactivated BayPanel for purchased car {carId}");
            }
        }
    }

    /// <summary>
    /// Update user data from the new response format
    /// </summary>
    private void UpdateUserDataFromNewFormat(PurchaseResponse response, string carId)
    {
        ownedCars.Clear();

        // Process car data
        foreach (UserCarData car in response.user.user_cars)
        {
            if (!string.IsNullOrEmpty(car.car_id))
            {
                bool isActivated = car.is_activated == 1;
                ownedCars[car.car_id] = isActivated;
                Debug.Log($"Updated car ownership: {car.car_id}, Activated: {isActivated}");
            }
        }

        // Update the UI
        UpdateOwnedCarsText();

        // Update balance displays
        UpdateBalanceDisplays(response.user.balance, response.user.referral_balance);

        // Set car as owned and update buttons
        ownedCars[carId] = true;
        UpdateButtonStates(carId, true, true);

        // Update status
        DisplaySuccessMessage("Car purchased successfully!");
    }

    /// <summary>
    /// Update user data from the old response format
    /// </summary>
    private void UpdateUserDataFromOldFormat(PurchaseResponse response, string carId)
    {
        bool balanceUpdated = false;

        // Update balance displays
        balanceUpdated = UpdateBalanceDisplays(response.balance, response.referral_balance);

        // Set car as owned
        ownedCars[carId] = true;
        UpdateButtonStates(carId, true, true);

        // Update owned cars list
        UpdateOwnedCarsText();

        // Update status with success message
        DisplaySuccessMessage("Car purchased successfully!");

        // If no balance was updated, refresh user data
        if (!balanceUpdated)
        {
            Debug.Log("No balance information in response - refreshing user data");
            DisplaySuccessMessage("Car purchased, refreshing data...");
            FetchUserData();
        }
    }

    /// <summary>
    /// Update balance displays from response values
    /// </summary>
    private bool UpdateBalanceDisplays(float? balance, float? referralBalance)
    {
        bool updated = false;

        // Update regular balance
        if (balance.HasValue && balanceText != null)
        {
            balanceText.text = $"{balance:F2}";
            Debug.Log($"Updated balance: ${balance:F2}");
            updated = true;
        }

        // Update referral balance
        if (referralBalance.HasValue && referralBalanceText != null)
        {
            referralBalanceText.text = $"Referral Balance: ${referralBalance:F2}";
            Debug.Log($"Updated referral balance: ${referralBalance:F2}");
            updated = true;
        }

        return updated;
    }

    #endregion

    /// <summary>
    /// Process the activation response from the server
    /// </summary>
    private void ProcessActivationResponse(string responseText, string carId)
    {
        debug_log("Processing activation response");

        try
        {
            string validJson = EnsureJsonWrapper(FilterOutPhpWarnings(responseText));
            ActivateCarResponse response = JsonConvert.DeserializeObject<ActivateCarResponse>(validJson);

            // Validate response
            if (!ValidateActivationResponse(response))
                return;

            // Update nonce if available
            UpdateNonceFromActivationResponse(response);

            // Process successful activation
            if (response.status == "success")
            {
                ProcessSuccessfulActivation(response, carId);
            }
            else
            {
                string errorMessage = response.message ?? "Unknown error";
                debug_log($"Activation failed: {errorMessage}");
                DisplayErrorMessage("Activation failed. Please try again.");
            }
        }
        catch (Exception e)
        {
            debug_log($"Exception parsing activation response: {e.Message}");
            DisplayErrorMessage("Error: Unable to process activation");
        }
    }

    /// <summary>
    /// Validate the activation response
    /// </summary>
    private bool ValidateActivationResponse(ActivateCarResponse response)
    {
        if (response == null || (response.status != "success" && response.rawResponse == null))
        {
            debug_log("Failed to parse response as ActivateCarResponse");
            DisplayErrorMessage("Error: Invalid response format");
            return false;
        }

        if (response.rawResponse != null)
        {
            debug_log("Raw response detected");
            DisplayErrorMessage("Error: Server issue");
            return false;
        }

        return true;
    }

    /// <summary>
    /// Update nonce from activation response
    /// </summary>
    private void UpdateNonceFromActivationResponse(ActivateCarResponse response)
    {
        if (SessionManager.Instance != null && !string.IsNullOrEmpty(response.nonce))
        {
            SessionManager.Instance.UpdateNonce(response.nonce);
            debug_log($"Nonce updated to: {response.nonce}");
            SecurePlayerPrefsSet("LastNonce", response.nonce);
        }
    }

    /// <summary>
    /// Process a successful activation response
    /// </summary>
    private void ProcessSuccessfulActivation(ActivateCarResponse response, string carId)
    {
        // Update balance display
        UpdateBalanceFromActivationResponse(response);

        // Display success message
        string carName = carNames.TryGetValue(carId, out string name) ? name : "This car";
        DisplaySuccessMessage($"Successfully activated {carName}!");
        debug_log($"Activation successful for car: {carId}");

        // Update the vehicle selection system
        UpdateVehicleAfterActivation(carId);

        // Update car ownership data
        UpdateCarOwnershipData(response, carId);

        // Update UI
        UpdateOwnedCarsText();
        UpdateCurrentVehicleUIIfNeeded(carId);
    }

    /// <summary>
    /// Update balance display from activation response
    /// </summary>
    private void UpdateBalanceFromActivationResponse(ActivateCarResponse response)
    {
        if (response.user != null && response.user.balance.HasValue)
        {
            if (balanceText != null) balanceText.text = $"{response.user.balance:F2}";
            SecurePlayerPrefsSet("UserBalance", response.user.balance.ToString());
            debug_log($"Updated user balance: {response.user.balance}");
        }
        else if (response.balance.HasValue)
        {
            if (balanceText != null) balanceText.text = $"{response.balance:F2}";
            SecurePlayerPrefsSet("UserBalance", response.balance.ToString());
            debug_log($"Updated balance: {response.balance}");
        }
    }

    /// <summary>
    /// Update vehicle after activation
    /// </summary>
    private void UpdateVehicleAfterActivation(string carId)
    {
        if (vehicleSetting == null || currentVehicleNumber < 0 || currentVehicleNumber >= vehicleSetting.Length)
            return;

        // Set the current vehicle as the active one
        PlayerPrefs.SetInt("CurrentVehicle", currentVehicleNumber);

        // Update the UI
        UpdateVehicleUI();

        // Explicitly set panel states for activated car
        UpdatePanelsAfterActivation(carId);
    }

    /// <summary>
    /// Update panels after activation
    /// </summary>
    private void UpdatePanelsAfterActivation(string carId)
    {
        if (customizeVehiclePanel != null)
        {
            customizeVehiclePanel.SetActive(true);
            debug_log($"UpdatePanelsAfterActivation: Activated customizeVehiclePanel for car {carId}");
        }

        if (BayPanel != null)
        {
            BayPanel.SetActive(false);
            debug_log($"UpdatePanelsAfterActivation: Deactivated BayPanel for car {carId}");
        }
    }

    /// <summary>
    /// Update car ownership data from response
    /// </summary>
    private void UpdateCarOwnershipData(ActivateCarResponse response, string carId)
    {
        if (response.user != null && response.user.user_cars != null)
        {
            try
            {
                UserCarData[] cars_data = response.user.user_cars;
                debug_log($"Received {cars_data.Length} car data items from user_cars table");
                foreach (UserCarData carData in cars_data)
                {
                    string carIdFromServer = carData.car_id;
                    bool isActivated = carData.is_activated == 1;
                    ownedCars[carIdFromServer] = isActivated;
                    debug_log($"Updated car from user_cars table: {carIdFromServer}, Activated: {isActivated}");
                    SecurePlayerPrefsSet($"Car_{carIdFromServer}", isActivated ? "1" : "0");
                }
            }
            catch (Exception ex)
            {
                debug_log($"Error processing user_cars data: {ex.Message}");
            }
        }
        else
        {
            ownedCars[carId] = true;
            SecurePlayerPrefsSet($"Car_{carId}", "1");
            debug_log($"Car {carId} activated without server car data");
        }
    }

    /// <summary>
    /// Update the current vehicle UI if this is the selected vehicle
    /// </summary>
    private void UpdateCurrentVehicleUIIfNeeded(string carId)
    {
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            if (vehicleSetting[currentVehicleNumber].id == carId)
            {
                UpdateVehicleUI();
            }
        }
    }

    private IEnumerator FetchUserDataThenActivateCar(string carId)
    {
        debug_log($"Fetching user data before activating car: {carId}");

        // Reset status text color to default
        ResetStatusTextColor();

        // اطلاع رسانی به کاربر
        if (statusText != null)
            statusText.text = "Updating user data before activation...";

        // دریافت اطلاعات کاربر
        yield return StartCoroutine(GetUserDataCoroutine());

        // Check if this is a free car
        bool isFreeCar = false;
        if (carPrices.TryGetValue(carId, out float price))
        {
            isFreeCar = price == 0;
        }

        // Get car name if available
        string carName = carNames.TryGetValue(carId, out string name) ? name : "This car";

        // بررسی آیا ماشین در لیست ماشین‌های خریداری شده وجود دارد
        if (ownedCars.TryGetValue(carId, out bool isActivated))
        {
            if (isActivated)
            {
                debug_log($"Car {carId} is already activated");
                if (isFreeCar)
                    DisplaySuccessMessage("Free car is ready to use");
                else
                    DisplaySuccessMessage($"{carName} is already activated");
            }
            else
            {
                debug_log($"Car {carId} is owned but not activated. Starting activation...");
                ResetStatusTextColor();
                if (statusText != null)
                    statusText.text = $"Starting {carName} activation...";

                // شروع فعال‌سازی خودرو
                StartCoroutine(ApiRequestCoroutine(ApiOperation.ActivateCar, carId));
            }
        }
        else
        {
            debug_log($"Car {carId} is not in the owned cars list");
            if (statusText != null)
            {
                if (isFreeCar)
                    statusText.text = "Free car is available";
                else
                    statusText.text = $"You need to purchase {carName} first";
            }
        }
    }

    // Method to handle Next button click
    public void OnNextButtonClick()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        // Use the vehicle selection system
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            VehicleSetting selectedVehicle = vehicleSetting[currentVehicleNumber];

            // Check if the vehicle is bought
            if (selectedVehicle.Bought)
            {
                // Save the current vehicle selection
                PlayerPrefs.SetInt("CurrentVehicle", currentVehicleNumber);
                PlayerPrefs.Save();

                debug_log($"Next button clicked for vehicle {selectedVehicle.name}");
                DisplaySuccessMessage($"Selected {selectedVehicle.name} for race!");

                return;
            }
            else
            {
                // This is just informational, not an error
                ResetStatusTextColor();
                if (statusText != null) statusText.text = "You need to purchase this vehicle first";
                debug_log("Next button clicked but vehicle is not purchased");
                return;
            }
        }
        else
        {
            DisplayErrorMessage("Error: No vehicle selected");
            return;
        }
    }

    // Method to check car status without using nonce
    public void CheckCarStatus()
    {
        string carId;

        // Get the car ID from the vehicle selection system
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            // Use the vehicle selection system
            if (!string.IsNullOrEmpty(vehicleSetting[currentVehicleNumber].id))
            {
                carId = vehicleSetting[currentVehicleNumber].id;
            }
            else
            {
                carId = $"Car:{currentVehicleNumber}";
            }
        }
        else
        {
            if (statusText != null) statusText.text = "Error: No vehicle selected";
            return;
        }

        if (statusText != null) statusText.text = "Checking car status...";

        StartCoroutine(CheckCarStatusCoroutine(carId));
    }

    private IEnumerator CheckCarStatusCoroutine(string carId)
    {
        // Reset status text color to default
        ResetStatusTextColor();

        // We'll use a simpler GET request to check car status without nonce
        string token = "";
        string key1 = "";

        // Get token and key1 from SessionManager or Login class
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            token = SessionManager.Instance.JWT_Token;
            key1 = SessionManager.Instance.Key1;
        }
        else if (!string.IsNullOrEmpty(Login.JWT_Token))
        {
            token = Login.JWT_Token;
            key1 = Login.Key1;
        }
        else
        {
            debug_log("No authentication token available");
            DisplayErrorMessage("Error: Not logged in");
            yield break;
        }

        // Build the URL with the car_id and key1 as parameters (using 24-hour token system)
        string url = userDataApiUrl + "?check_car=" + carId + "&key1=" + UnityWebRequest.EscapeURL(key1);

        // Create the web request
        UnityWebRequest request = UnityWebRequest.Get(url);

        // Add Authorization header with Bearer token
        request.SetRequestHeader("Authorization", "Bearer " + token);

        debug_log($"Sending car status check request");
        yield return request.SendWebRequest();

        if (request.result != UnityWebRequest.Result.Success)
        {
            debug_log($"Car status check error: {request.error}");
            DisplayErrorMessage("Error: Unable to check status");
            yield break;
        }

        string responseText = request.downloadHandler.text;
        debug_log($"Car status check response received");

        try
        {
            // Parse the response
            var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseText);

            if (response != null && response.TryGetValue("status", out object statusObj) && statusObj.ToString() == "success")
            {
                if (response.TryGetValue("car_status", out object carStatusObj))
                {
                    var carStatus = JsonConvert.DeserializeObject<Dictionary<string, object>>(carStatusObj.ToString());

                    if (carStatus != null &&
                        carStatus.TryGetValue("is_owned", out object isOwnedObj) &&
                        carStatus.TryGetValue("is_activated", out object isActivatedObj))
                    {
                        bool isOwned = Convert.ToBoolean(isOwnedObj);
                        bool isActivated = Convert.ToBoolean(isActivatedObj);

                        // Update car status in memory
                        if (isOwned)
                        {
                            ownedCars[carId] = isActivated;

                            // Update current vehicle if this is the selected vehicle
                            if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
                            {
                                if (vehicleSetting[currentVehicleNumber].id == carId)
                                {
                                    vehicleSetting[currentVehicleNumber].Bought = true;
                                    UpdateVehicleUI();
                                }
                            }
                        }

                        // Update UI
                        UpdateButtonStates(carId, isOwned, isActivated);
                        UpdateOwnedCarsText();

                        // Update status text
                        if (statusText != null)
                        {
                            // Check if this is a free car
                            bool isFreeCar = false;
                            if (carPrices.TryGetValue(carId, out float price))
                            {
                                isFreeCar = price == 0;
                            }

                            // Get car name if available
                            string carName = carNames.TryGetValue(carId, out string name) ? name : "This car";

                            if (isFreeCar)
                            {
                                if (isActivated)
                                    DisplaySuccessMessage("Free car is ready to use");
                                else
                                    DisplaySuccessMessage("Free car is available");
                            }
                            else if (isOwned)
                            {
                                if (isActivated)
                                    DisplaySuccessMessage($"{carName} is owned and active");
                                else {
                                    // This is just informational, not a success message
                                    ResetStatusTextColor();
                                    if (statusText != null) statusText.text = $"{carName} is owned but not active";
                                }
                            }
                            else
                            {
                                // This is just informational, not an error
                                ResetStatusTextColor();
                                if (statusText != null) statusText.text = $"{carName} is not owned";
                            }
                        }
                    }
                }
            }
            else
            {
                DisplayErrorMessage("Error checking car status");
            }
        }
        catch (Exception ex)
        {
            debug_log($"Error parsing car status response: {ex.Message}");
            DisplayErrorMessage("Error checking car status");
        }
    }

    // Add new method to fetch car list from server
    private IEnumerator FetchCarListCoroutine()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Loading car list...";
        }

        // Start API request to get car list
        yield return StartCoroutine(ApiRequestCoroutine(ApiOperation.GetAllCars));
    }

    // Update vehicle data with information from server
    private void UpdateVehicleData(CarInfo[] cars)
    {
        debug_log($"Updating vehicle data with {cars.Length} cars from server");

        // Clear existing dictionaries
        carPrices.Clear();
        carNames.Clear();

        // Add car data to dictionaries
        foreach (var car in cars)
        {
            carPrices[car.id] = car.price;
            carNames[car.id] = car.name;
        }

        // Update vehicle settings with server data if possible
        if (vehicleSetting != null)
        {
            for (int i = 0; i < vehicleSetting.Length; i++)
            {
                string vehicleId = vehicleSetting[i].id;
                if (string.IsNullOrEmpty(vehicleId))
                {
                    vehicleId = $"Car:{i}";
                    vehicleSetting[i].id = vehicleId;
                }

                // Try to find matching car info from server
                foreach (var car in cars)
                {
                    if (car.id == vehicleId)
                    {
                        // Update vehicle name and price from server
                        vehicleSetting[i].name = car.name;
                        vehicleSetting[i].price = (int)car.price;
                        break;
                    }
                }
            }
        }

        // Update the current vehicle UI
        if (currentVehicle != null)
        {
            UpdateVehicleUI();
        }
    }

    // Add a new ProcessCarListResponse method to handle the GetAllCars response
    private void ProcessCarListResponse(string responseText)
    {
        debug_log("Processing car list response");

        try
        {
            // Parse the response JSON
            CarListResponse response = JsonConvert.DeserializeObject<CarListResponse>(responseText);

            // Check if response is valid
            if (response == null)
            {
                debug_log("Invalid car list response format");
                DisplayErrorMessage("Error: Invalid response");
                return;
            }

            // Update SessionManager with new nonce if one is provided
            if (SessionManager.Instance != null && !string.IsNullOrEmpty(response.nonce))
            {
                SessionManager.Instance.UpdateNonce(response.nonce);
                debug_log($"Updated nonce: {response.nonce}");
            }

            if (response.status == "success" && response.cars != null && response.cars.Length > 0)
            {
                debug_log($"Successfully retrieved {response.cars.Length} cars from server");

                // Update vehicle data with car list from server
                UpdateVehicleData(response.cars);

                DisplaySuccessMessage("Car list loaded successfully");
            }
            else
            {
                string errorMessage = response.message ?? "Failed to load car list";
                debug_log("Error: " + errorMessage);

                DisplayErrorMessage("Error: " + errorMessage);
            }
        }
        catch (Exception e)
        {
            debug_log("Exception parsing car list response: " + e.Message);

            DisplayErrorMessage("Error loading car list");
        }
    }

    public void OnPurchaseWithReferralButtonClick()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        // Get the car ID from the vehicle selection system
        if (vehicleSetting != null && currentVehicleNumber >= 0 && currentVehicleNumber < vehicleSetting.Length)
        {
            string carId;
            if (!string.IsNullOrEmpty(vehicleSetting[currentVehicleNumber].id))
            {
                carId = vehicleSetting[currentVehicleNumber].id;
            }
            else
            {
                carId = $"Car:{currentVehicleNumber}";
            }

            StartCoroutine(PurchaseWithReferralBalanceCoroutine(carId));
        }
        else
        {
            DisplayErrorMessage("Error: No vehicle selected");
        }
    }

    // Method to handle refresh button click
    public void OnRefreshButtonClick()
    {
        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Refreshing data...";
        }

        // Refresh car list and user data
        StartCoroutine(RefreshDataCoroutine());
    }

    private IEnumerator RefreshDataCoroutine()
    {
        // Show loading indicator if available
        if (loadingPay != null)
        {
            loadingPay.SetActive(true);
        }

        // First fetch car list from server
        yield return StartCoroutine(FetchCarListCoroutine());

        // Then fetch user data
        yield return StartCoroutine(GetUserDataCoroutine());

        // Check car status with server
        CheckCarStatus();

        // Hide loading indicator
        if (loadingPay != null)
        {
            loadingPay.SetActive(false);
        }

        DisplaySuccessMessage("Data refreshed successfully");
    }

    private IEnumerator PurchaseWithReferralBalanceCoroutine(string carId)
    {
        Debug.Log($"Starting purchase with referral balance for car: {carId}");

        // Reset status text color to default
        ResetStatusTextColor();

        if (statusText != null)
        {
            statusText.text = "Purchasing car with referral balance...";
        }

        // Check if car price is available
        if (!carPrices.TryGetValue(carId, out float carPrice))
        {
            Debug.LogError($"Car price not found for ID: {carId}");
            DisplayErrorMessage("Error: Invalid car ID");
            yield break;
        }

        // Parse current referral balance
        float referralBalance = 0;
        if (referralBalanceText != null)
        {
            string balanceText = referralBalanceText.text;
            if (balanceText.Contains("$"))
            {
                string[] parts = balanceText.Split('$');
                if (parts.Length > 1)
                {
                    float.TryParse(parts[1].Trim(), out referralBalance);
                }
            }
        }

        // Check if user has enough referral balance
        if (referralBalance < carPrice)
        {
            Debug.LogError($"Insufficient referral balance: {referralBalance} < {carPrice}");
            DisplayErrorMessage($"Not enough referral balance to purchase this car (need ${carPrice:F2})");
            yield break;
        }

        Debug.Log($"Car price: ${carPrice:F2}, Referral balance: ${referralBalance:F2}");

        // Call API to purchase car with referral balance
        yield return StartCoroutine(ApiRequestCoroutine(ApiOperation.PurchaseCar, carId, true));
    }


}