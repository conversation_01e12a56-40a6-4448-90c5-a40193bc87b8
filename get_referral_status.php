<?php
/**
 * Get Referral Status API
 *
 * This API fetches user's referral data including:
 * - User's referral code
 * - Referral balance
 * - List of referred users
 * - Referral rewards status
 *
 * All operations require JWT token authentication
 */

// Disable error display
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Log errors to a file with specific directory
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/logs/referral_errors.log');

// Create logs directory if it doesn't exist
if (!file_exists(dirname(__FILE__) . '/logs')) {
    mkdir(dirname(__FILE__) . '/logs', 0777, true);
}

// Buffer output to prevent any unwanted output before headers
ob_start();

require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers for JSON response and CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Helper function to safely log debug info
function debug_log($message) {
    $log_file = dirname(__FILE__) . '/logs/referral_debug.log';
    if (defined('ENABLE_DEBUG_LOG') && ENABLE_DEBUG_LOG) {
        error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, $log_file);
    }
}

/**
 * Send a JSON response
 *
 * @param array $data The data to send
 * @param int $status_code The HTTP status code
 */
if (!function_exists('respondJSON')) {
    function respondJSON($data, $status_code = 200) {
        // Clear any existing output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Set the HTTP response code
        http_response_code($status_code);

        // Set headers
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Ensure we have a status in the response
        if (!isset($data['status'])) {
            $data['status'] = $status_code >= 200 && $status_code < 300 ? 'success' : 'error';
        }

        // Add timestamp
        $data['timestamp'] = date('Y-m-d H:i:s');

        // Convert the response to JSON with error handling
        $json = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        if ($json === false) {
            error_log("JSON encoding error: " . json_last_error_msg());
            $json = json_encode([
                'status' => 'error',
                'message' => 'Failed to encode response',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }

        // Output the response
        echo $json;
        exit;
    }
}

// Helper function to get the token
if (!function_exists('getBearerToken')) {
    function getBearerToken() {
        $headers = null;
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }

        if (!empty($headers) && preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
        return null;
    }
}

// Run all code in a try-catch block for error handling
try {
    // Clear any existing output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }
    ob_start();

    // Only accept POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    debug_log("Referral API request received");

    // Get input data from POST with proper error handling
    $raw_input = file_get_contents('php://input');
    if ($raw_input === false) {
        throw new Exception('Failed to read input data');
    }

    $postData = json_decode($raw_input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }

    if (!$postData) {
        $postData = $_POST;
    }

    // Validate required fields
    if (!isset($postData['key1']) || !isset($postData['nonce'])) {
        respondJSON(['status' => 'error', 'message' => 'Key1 and nonce are required'], 400);
    }

    $key1 = $postData['key1'];
    $nonce = $postData['nonce'];

    // Get authorization token
    $token = getBearerToken();
    debug_log("Token received: " . ($token ? "Yes" : "No"));

    if (!$token) {
        respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
    }

    // Verify token
    $user_id = verifyJWT($token);
    debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

    if (!$user_id) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
    }

    // If somehow user_id is still an array, extract the ID
    if (is_array($user_id) && isset($user_id['user_id'])) {
        $user_id = $user_id['user_id'];
        debug_log("Extracted user_id from array: " . $user_id);
    }

    // Verify Key1
    if (!verifyUserKey($user_id, $key1)) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
    }

    // Verify Nonce
    if (!verifyAndUseNonce($user_id, $nonce)) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or used nonce'], 401);
    }

    // Generate a new nonce for the next request
    $new_nonce = generateNonce($user_id);
    debug_log("New nonce generated: $new_nonce");

    // Get user's referral data
    // 1. Get user's referral code, balance, and referral wins
    $stmt = $conn->prepare("SELECT username, referral_code, referral_balance, referral_win FROM users WHERE id = ?");
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        respondJSON([
            'status' => 'error',
            'message' => 'User not found',
            'nonce' => $new_nonce
        ], 404);
    }

    $user_data = $result->fetch_assoc();

    // 2. Get user's referrals
    $stmt = $conn->prepare("
        SELECT r.*, u.username as referred_username, u.created_at as registration_date
        FROM referrals r
        JOIN users u ON r.referred_id = u.id
        WHERE r.referrer_id = ?
        ORDER BY r.created_at DESC
    ");
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $referrals_result = $stmt->get_result();

    $referrals = [];

    while ($referral = $referrals_result->fetch_assoc()) {
        $referrals[] = [
            'id' => (int)$referral['id'],
            'referred_username' => $referral['referred_username'],
            'registration_date' => $referral['registration_date'],
            'registration_rewarded' => (bool)$referral['registration_rewarded'],
            'race_rewarded' => (bool)$referral['race_rewarded'],
            'created_at' => $referral['created_at']
        ];
    }

    // 3. Calculate total rewards
    $total_registration_rewards = 0;
    $total_race_rewards = 0;
    $total_referred_users = count($referrals);
    $completed_referrals = 0;

    foreach ($referrals as $referral) {
        if ($referral['registration_rewarded']) {
            $total_registration_rewards += 0.5; // Registration reward
        }

        if ($referral['race_rewarded']) {
            $total_race_rewards += 1.0; // First race reward
            $completed_referrals++;
        }
    }

    // 4. Generate stats
    $stats = [
        'total_referred_users' => $total_referred_users,
        'total_registration_rewards' => $total_registration_rewards,
        'total_race_rewards' => $total_race_rewards,
        'total_rewards' => $total_registration_rewards + $total_race_rewards,
        'completed_referrals' => $completed_referrals,
        'pending_referrals' => $total_referred_users - $completed_referrals
    ];

    // 5. Generate share message (for copying to clipboard)
    $share_message = "Join me in this exciting racing game! Use my referral code: {$user_data['referral_code']} to get special rewards!";

    // 6. Return success response
    respondJSON([
        'status' => 'success',
        'username' => $user_data['username'],
        'referral_code' => $user_data['referral_code'],
        'referral_balance' => (float)$user_data['referral_balance'],
        'referral_win' => (float)$user_data['referral_win'],
        'referrals' => $referrals,
        'stats' => $stats,
        'share_message' => $share_message,
        'nonce' => $new_nonce
    ]);

} catch (Exception $e) {
    // Log the error with full details
    error_log(sprintf(
        "Error in get_referral_status.php: %s\nFile: %s\nLine: %d\nTrace:\n%s",
        $e->getMessage(),
        $e->getFile(),
        $e->getLine(),
        $e->getTraceAsString()
    ));

    // Clear any existing output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Return a clean error response
    respondJSON([
        'status' => 'error',
        'message' => 'Server error occurred. Please try again later.',
        'error_code' => 'SERVER_ERROR',
        'nonce' => isset($new_nonce) ? $new_nonce : null
    ], 500);
}

// Ensure we end any remaining output buffers
while (ob_get_level()) {
    ob_end_clean();
}
