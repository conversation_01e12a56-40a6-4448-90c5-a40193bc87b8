
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using Newtonsoft.Json;

/// <summary>
/// This class is responsible for spawning the player's active car based on server data
/// instead of using PlayerPrefs which can be easily manipulated by the user.
/// </summary>
public class CarSpawner : MonoBehaviour
{
    [Header("Car Settings")]
    [Tooltip("Array of car prefabs that can be spawned")]
    public GameObject[] carPrefabs;

    [<PERSON><PERSON>("Server Settings")]
    [Tooltip("URL to get the active car from the server")]
    public string getActiveCarUrl = "https://game-gofaster.com/gam/api/get_active_car.php";

    [Header("Fallback Settings")]
    [Tooltip("Index of the default car to spawn if server request fails")]
    public int defaultCarIndex = 0;

    [Header("References")]
    [Tooltip("Reference to the spawned car")]
    private GameObject spawnedCar;

    // Start is called before the first frame update
    void Start()
    {
        // Get the active car from the server
        StartCoroutine(GetActiveCarFromServer());
    }

    /// <summary>
    /// Coroutine to get the active car from the server
    /// </summary>
    private IEnumerator GetActiveCarFromServer()
    {
        // Check if we have authentication token
        string token = "";
        if (SessionManager.Instance != null && SessionManager.Instance.IsLoggedIn)
        {
            token = SessionManager.Instance.JWT_Token;
        }
        else if (!string.IsNullOrEmpty(Login.JWT_Token))
        {
            token = Login.JWT_Token;
        }
        else
        {
            Debug.LogError("No authentication token available");
            SpawnDefaultCar();
            yield break;
        }

        // Get nonce for security
        string nonce = "";
        if (SessionManager.Instance != null)
        {
            nonce = SessionManager.Instance.CurrentNonce;
        }
        else if (!string.IsNullOrEmpty(Login.CurrentNonce))
        {
            nonce = Login.CurrentNonce;
        }

        // Build the URL with nonce parameter
        string url = getActiveCarUrl;
        if (!string.IsNullOrEmpty(nonce))
        {
            url += (url.Contains("?") ? "&" : "?") + "nonce=" + UnityWebRequest.EscapeURL(nonce);
        }

        Debug.Log($"Getting active car from server: {url}");

        // Create the web request
        UnityWebRequest request = UnityWebRequest.Get(url);
        request.SetRequestHeader("Authorization", "Bearer " + token);

        // Send the request
        yield return request.SendWebRequest();

        // Log the raw response for debugging
        Debug.Log($"Server response: {request.downloadHandler.text}");

        // Handle response
        if (request.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError($"Error getting active car: {request.error}");
            SpawnDefaultCar();
            yield break;
        }

        // Process the response
        string responseText = request.downloadHandler.text;
        Debug.Log($"Active car response: {responseText}");

        try
        {
            // Parse the response
            var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseText);

            if (response != null && response.ContainsKey("status") && response["status"].ToString() == "success")
            {
                // Get the active car ID
                if (response.ContainsKey("active_car"))
                {
                    string activeCarId = response["active_car"].ToString();
                    Debug.Log($"Active car ID from server: {activeCarId}");

                    int carIndex = GetCarIndexFromId(activeCarId);

                    // Spawn the active car
                    SpawnCar(carIndex);
                }
                else
                {
                    Debug.LogWarning("No active car found in response");
                    SpawnDefaultCar();
                }
            }
            else
            {
                string errorMessage = response != null && response.ContainsKey("message") ?
                    response["message"].ToString() : "Unknown error";
                Debug.LogError($"Error in active car response: {errorMessage}");
                SpawnDefaultCar();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error parsing active car response: {ex.Message}");
            SpawnDefaultCar();
        }
    }

    /// <summary>
    /// Convert car ID to index in the carPrefabs array
    /// </summary>
    private int GetCarIndexFromId(string carId)
    {
        Debug.Log($"Converting car ID to index: {carId}");

        // This implementation depends on your car ID format
        // Example: "Car:1" -> 1
        if (carId.StartsWith("Car:"))
        {
            string indexStr = carId.Substring(4);
            Debug.Log($"Extracted index string: {indexStr}");

            if (int.TryParse(indexStr, out int index))
            {
                Debug.Log($"Parsed index: {index}");

                if (index >= 0 && index < carPrefabs.Length)
                {
                    Debug.Log($"Using car index: {index}");
                    return index;
                }
                else
                {
                    Debug.LogWarning($"Car index out of range: {index}, valid range is 0-{carPrefabs.Length - 1}");
                }
            }
            else
            {
                Debug.LogWarning($"Failed to parse car index from: {indexStr}");
            }
        }
        else
        {
            Debug.LogWarning($"Car ID does not start with 'Car:': {carId}");
        }

        // If car ID is not in the expected format or index is out of range,
        // return the default car index
        Debug.LogWarning($"Using default car index: {defaultCarIndex}");
        return defaultCarIndex;
    }

    /// <summary>
    /// Spawn the car with the given index
    /// </summary>
    private void SpawnCar(int carIndex)
    {
        // Clamp the index to valid range
        carIndex = Mathf.Clamp(carIndex, 0, carPrefabs.Length - 1);

        // Destroy any existing car
        if (spawnedCar != null)
        {
            Destroy(spawnedCar);
        }

        // Spawn the new car at this GameObject's position and rotation
        spawnedCar = Instantiate(carPrefabs[carIndex], transform.position, transform.rotation);

        Debug.Log($"Spawned car with index {carIndex} at position {transform.position}");
    }

    /// <summary>
    /// Spawn the default car
    /// </summary>
    private void SpawnDefaultCar()
    {
        SpawnCar(defaultCarIndex);
    }
}