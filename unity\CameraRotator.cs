using UnityEngine;

/// <summary>
/// Handles camera rotation around a target object
/// </summary>
public class CameraRotator : MonoBehaviour
{
    [Header("Camera Settings")]
    [SerializeField] public float cameraRotateSpeed = 5;
    [SerializeField] public Camera mainCamera; // Reference to the main camera
    [SerializeField] public Transform targetObject; // Object to rotate around (previously vehicleRoot in PurchaseCar)

    // Camera rotation variables
    private float x, y = 0;

    private void Start()
    {
        // Find main camera if not assigned
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            Debug.Log("Main camera automatically assigned in CameraRotator");
        }

        // Check if target object is assigned
        if (targetObject == null)
        {
            Debug.LogWarning("No target object assigned to CameraRotator. Camera rotation will not work properly.");
        }
    }

    private void Update()
    {
        // Skip if camera or target object is not available
        if (targetObject == null) return;
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            if (mainCamera == null) return;
        }

        // Handle camera rotation around target object
        if (Input.touchCount == 1)
        {
            switch (Input.GetTouch(0).phase)
            {
                case TouchPhase.Moved:
                    x = Mathf.Lerp(x, Mathf.Clamp(Input.GetTouch(0).deltaPosition.x, -2, 2) * cameraRotateSpeed, Time.deltaTime * 3.0f);
                    mainCamera.fieldOfView = Mathf.Clamp(mainCamera.fieldOfView, 50, 60);
                    mainCamera.fieldOfView = Mathf.Lerp(mainCamera.fieldOfView, 50, Time.deltaTime);
                    break;
            }
        }
        else
        {
            x = Mathf.Lerp(x, cameraRotateSpeed * 0.02f, Time.deltaTime * 3.0f);
            mainCamera.fieldOfView = Mathf.Lerp(mainCamera.fieldOfView, 60, Time.deltaTime);
        }

        // Rotate camera around target object
        mainCamera.transform.RotateAround(targetObject.position, Vector3.up, x);
    }

    /// <summary>
    /// Set the target object to rotate around
    /// </summary>
    public void SetTargetObject(Transform target)
    {
        targetObject = target;
        Debug.Log($"Camera rotation target set to: {(target != null ? target.name : "null")}");
    }
}
