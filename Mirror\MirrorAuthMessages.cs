using System;
using Mirror;
using UnityEngine;

/// <summary>
/// Shared authentication messages used by both client and server
/// This ensures message types are consistent across both sides
/// </summary>
public static class MirrorAuthMessages
{
    // This static constructor ensures the class is initialized before others
    static MirrorAuthMessages()
    {
        Debug.Log("[DEBUG] MirrorAuthMessages - Static constructor called, message types initialized");
    }
    
    // Authentication message structures (used by both client and server)
    [Serializable]
    public struct AuthRequestMessage : NetworkMessage
    {
        public string token;
        public string key1;
    }
    
    [Serializable]
    public struct AuthResponseMessage : NetworkMessage
    {
        public bool success;
        public string message;
    }
} 