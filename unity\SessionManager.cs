using UnityEngine;
using System;
using System.Security.Cryptography;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// مدیریت داده‌های نشست کاربر در تمام صحنه‌ها با امنیت بالا
/// </summary>
public class SessionManager : MonoBehaviour
{
    // نمونه سینگلتون
    public static SessionManager Instance { get; private set; }

    // رابط عمومی برای دسترسی به داده‌های نشست - برای سازگاری با کدهای موجود
    // برای دسترسی خارجی، به ویژه MirrorClientAuthenticator
    public string JWT_Token => GetSessionValue("JWT_Token");
    public string Key1 => GetSessionValue("Key1");
    public string CurrentNonce => GetSessionValue("Nonce");
    public string Username => GetSessionValue("Username");

    // وضعیت ورود به سیستم
    public bool IsLoggedIn => !string.IsNullOrEmpty(JWT_Token) && !string.IsNullOrEmpty(Key1);

    // کلید برای رمزنگاری (در محیط‌های واقعی باید امن‌تر باشد)
    private readonly byte[] _encryptionKey = new byte[] { 0x54, 0x68, 0x69, 0x73, 0x49, 0x73, 0x41, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79 };

    // دیکشنری داخلی برای ذخیره داده‌های نشست در حافظه
    private Dictionary<string, string> _sessionData = new Dictionary<string, string>();

    // مسیر ذخیره‌سازی داده‌ها
    private string _dataFilePath;

    // زمان آخرین فعالیت
    private DateTime _lastActivityTime;

    // مدت زمان انقضاء بر حسب دقیقه (24 ساعت = 1440 دقیقه)
    [SerializeField] private float _sessionTimeoutMinutes = 1440f;

    // آیا از PlayerPrefs نیز استفاده شود؟ (برای سازگاری با نسخه‌های قدیمی)
    [SerializeField] private bool _usePlayerPrefs = true;

    // آیا از رمزنگاری استفاده شود؟
    [SerializeField] private bool _useEncryption = true;

    // رویدادهای مدیریت نشست
    public event Action<string> OnLogin;
    public event Action OnLogout;
    public event Action<string> OnSessionError;

    private void Awake()
    {
        // الگوی سینگلتون
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);

        // تنظیم مسیر فایل دائمی
        _dataFilePath = Path.Combine(Application.persistentDataPath, "session_data.enc");

        // به‌روزرسانی زمان آخرین فعالیت
        _lastActivityTime = DateTime.Now;

        // Clear any existing session data
        Logout();

        // Delete the session file if it exists
        if (File.Exists(_dataFilePath))
        {
            try
            {
                File.Delete(_dataFilePath);
                Debug.Log("Deleted existing session file on startup");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error deleting session file: {e.Message}");
            }
        }

        Debug.Log("SessionManager initialized with no automatic login");
    }

    private void Update()
    {
        // بررسی انقضای نشست
        if (IsLoggedIn && (DateTime.Now - _lastActivityTime).TotalMinutes > _sessionTimeoutMinutes)
        {
            Debug.Log("Session timed out due to inactivity");
            Logout();
        }
    }

    /// <summary>
    /// تنظیم داده‌های نشست پس از ورود موفق به سیستم
    /// </summary>
    public void SetSessionData(string token, string key1, string nonce, string username)
    {
        // تنظیم داده‌های نشست
        SetSessionValue("JWT_Token", token);
        SetSessionValue("Key1", key1);
        SetSessionValue("Nonce", nonce);
        SetSessionValue("Username", username);
        SetSessionValue("LoginTime", DateTime.Now.ToString("o"));

        // به‌روزرسانی زمان آخرین فعالیت
        _lastActivityTime = DateTime.Now;

        // ذخیره داده‌های نشست
        SaveSessionData();

        // رویداد ورود به سیستم
        OnLogin?.Invoke(username);

        Debug.Log($"Enhanced session data set for user: {username}");
    }

    /// <summary>
    /// به‌روزرسانی نانس پس از تماس‌های API
    /// در سیستم توکن 24 ساعته، نانس برای مدت طولانی‌تری معتبر است
    /// </summary>
    public void UpdateNonce(string newNonce)
    {
        // اگر نانس جدید خالی باشد، آن را به‌روزرسانی نمی‌کنیم
        if (string.IsNullOrEmpty(newNonce))
        {
            Debug.LogWarning("Attempted to update nonce with empty value");
            return;
        }

        // اگر نانس فعلی با نانس جدید یکسان باشد، آن را به‌روزرسانی نمی‌کنیم
        if (newNonce == GetSessionValue("Nonce"))
        {
            Debug.Log("Nonce is already up to date");
            // فقط زمان آخرین فعالیت را به‌روزرسانی می‌کنیم
            _lastActivityTime = DateTime.Now;
            return;
        }

        Debug.Log($"Updating nonce from {GetSessionValue("Nonce")} to {newNonce}");
        SetSessionValue("Nonce", newNonce);

        // برای سازگاری با قبل
        if (_usePlayerPrefs)
        {
            PlayerPrefs.SetString("Nonce", newNonce);
            PlayerPrefs.Save();
        }

        SaveSessionData();

        // به‌روزرسانی زمان آخرین فعالیت
        _lastActivityTime = DateTime.Now;
    }

    /// <summary>
    /// فعالیت کاربر را ثبت می‌کند تا انقضای نشست را مدیریت کند
    /// </summary>
    public void RecordActivity()
    {
        _lastActivityTime = DateTime.Now;
    }

    /// <summary>
    /// تنظیم یک مقدار در نشست
    /// </summary>
    public void SetSessionValue(string key, string value)
    {
        if (_sessionData.ContainsKey(key))
        {
            _sessionData[key] = value;
        }
        else
        {
            _sessionData.Add(key, value);
        }

        // برای سازگاری با قبل
        if (_usePlayerPrefs)
        {
            PlayerPrefs.SetString(key, value);
        }

        SaveSessionData();
        RecordActivity();
    }

    /// <summary>
    /// دریافت یک مقدار از نشست
    /// </summary>
    public string GetSessionValue(string key)
    {
        RecordActivity();

        if (_sessionData.TryGetValue(key, out string value))
        {
            return value;
        }

        return string.Empty;
    }

    /// <summary>
    /// پاک کردن تمام داده‌های نشست (خروج از سیستم)
    /// </summary>
    public void Logout()
    {
        // پاک کردن داده‌های نشست
        _sessionData.Clear();

        // پاک کردن PlayerPrefs
        if (_usePlayerPrefs)
        {
            PlayerPrefs.DeleteKey("JWT_Token");
            PlayerPrefs.DeleteKey("Key1");
            PlayerPrefs.DeleteKey("Username");
            PlayerPrefs.DeleteKey("Nonce");
            PlayerPrefs.Save();
        }

        // حذف فایل ذخیره‌سازی
        try
        {
            if (File.Exists(_dataFilePath))
            {
                File.Delete(_dataFilePath);
                Debug.Log("Secure session file deleted");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error deleting session file: {e.Message}");
        }

        // رویداد خروج از سیستم
        OnLogout?.Invoke();

        Debug.Log("Session data cleared (user logged out)");
    }

    /// <summary>
    /// بارگذاری داده‌های نشست از محل ذخیره‌سازی
    /// </summary>
    private void LoadSessionData()
    {
        // ابتدا سعی در بارگذاری داده‌ها از حافظه امن
        bool loadedFromSecureStorage = LoadFromSecureStorage();

        // اگر بارگذاری از حافظه امن موفق نبود، از PlayerPrefs استفاده کنید
        if (!loadedFromSecureStorage && _usePlayerPrefs && PlayerPrefs.HasKey("JWT_Token") && PlayerPrefs.HasKey("Key1"))
        {
            _sessionData["JWT_Token"] = PlayerPrefs.GetString("JWT_Token");
            _sessionData["Key1"] = PlayerPrefs.GetString("Key1");
            _sessionData["Nonce"] = PlayerPrefs.GetString("Nonce");
            _sessionData["Username"] = PlayerPrefs.GetString("Username");

            // داده‌های بارگذاری شده را ذخیره می‌کند تا آنها را به حافظه امن منتقل کند
            SaveSessionData();

            Debug.Log($"Session data loaded from PlayerPrefs for user: {_sessionData["Username"]}");
        }
    }

    /// <summary>
    /// خواندن داده‌های نشست از حافظه امن
    /// </summary>
    private bool LoadFromSecureStorage()
    {
        try
        {
            if (File.Exists(_dataFilePath))
            {
                string encryptedData = File.ReadAllText(_dataFilePath);
                if (!string.IsNullOrEmpty(encryptedData))
                {
                    string decryptedData = _useEncryption ?
                        DecryptData(encryptedData) :
                        encryptedData;

                    if (!string.IsNullOrEmpty(decryptedData))
                    {
                        // تبدیل رشته به دیکشنری
                        DeserializeSessionData(decryptedData);

                        if (_sessionData.ContainsKey("Username"))
                        {
                            Debug.Log($"Secure session data loaded for user: {_sessionData["Username"]}");
                            return true;
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error loading secure session data: {e.Message}");
            OnSessionError?.Invoke("Error loading session data");
        }

        return false;
    }

    /// <summary>
    /// ذخیره داده‌های نشست به محل ذخیره‌سازی
    /// </summary>
    private void SaveSessionData()
    {
        try
        {
            // برای سازگاری با قبل
            if (_usePlayerPrefs)
            {
                if (_sessionData.TryGetValue("JWT_Token", out string token))
                    PlayerPrefs.SetString("JWT_Token", token);

                if (_sessionData.TryGetValue("Key1", out string key1))
                    PlayerPrefs.SetString("Key1", key1);

                if (_sessionData.TryGetValue("Nonce", out string nonce))
                    PlayerPrefs.SetString("Nonce", nonce);

                if (_sessionData.TryGetValue("Username", out string username))
                    PlayerPrefs.SetString("Username", username);

                PlayerPrefs.Save();
            }

            // ذخیره در حافظه امن
            SaveToSecureStorage();
        }
        catch (Exception e)
        {
            Debug.LogError($"Error saving session data: {e.Message}");
            OnSessionError?.Invoke("Error saving session data");
        }
    }

    /// <summary>
    /// ذخیره داده‌های نشست به حافظه امن
    /// </summary>
    private void SaveToSecureStorage()
    {
        if (_sessionData.Count == 0)
        {
            Debug.Log("No session data to save");
            return;
        }

        try
        {
            // سریالی کردن دیکشنری به رشته
            string serializedData = SerializeSessionData();

            // رمزنگاری داده‌ها (اختیاری)
            string dataToSave = _useEncryption ?
                EncryptData(serializedData) :
                serializedData;

            // ایجاد دایرکتوری اگر وجود ندارد
            string directory = Path.GetDirectoryName(_dataFilePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // ذخیره داده‌ها
            File.WriteAllText(_dataFilePath, dataToSave);

            Debug.Log("Session data saved to secure storage");
        }
        catch (Exception e)
        {
            Debug.LogError($"Error saving to secure storage: {e.Message}");
            OnSessionError?.Invoke("Error saving to secure storage");
        }
    }

    /// <summary>
    /// تبدیل دیکشنری داده‌های نشست به رشته
    /// </summary>
    private string SerializeSessionData()
    {
        // ساده‌سازی شده به عنوان مثال - می‌توان از JSON هم استفاده کرد
        return string.Join("|", _sessionData.Select(kvp => $"{kvp.Key}={kvp.Value}"));
    }

    /// <summary>
    /// تبدیل رشته به دیکشنری داده‌های نشست
    /// </summary>
    private void DeserializeSessionData(string data)
    {
        _sessionData.Clear();

        if (string.IsNullOrEmpty(data))
            return;

        string[] pairs = data.Split('|');
        foreach (string pair in pairs)
        {
            string[] keyValue = pair.Split(new[] { '=' }, 2);
            if (keyValue.Length == 2)
            {
                _sessionData[keyValue[0]] = keyValue[1];
            }
        }
    }

    /// <summary>
    /// رمزنگاری داده‌ها با AES
    /// </summary>
    private string EncryptData(string data)
    {
        if (string.IsNullOrEmpty(data))
            return string.Empty;

        try
        {
            using (Aes aes = Aes.Create())
            {
                aes.Key = _encryptionKey;
                aes.GenerateIV();

                byte[] iv = aes.IV;

                using (ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, iv))
                {
                    using (MemoryStream ms = new MemoryStream())
                    {
                        // ابتدا IV را ذخیره می‌کنیم
                        ms.Write(iv, 0, iv.Length);

                        using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter sw = new StreamWriter(cs))
                            {
                                sw.Write(data);
                            }
                        }

                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Encryption error: {e.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// رمزگشایی داده‌ها با AES
    /// </summary>
    private string DecryptData(string encryptedData)
    {
        if (string.IsNullOrEmpty(encryptedData))
            return string.Empty;

        try
        {
            byte[] encryptedBytes = Convert.FromBase64String(encryptedData);

            using (Aes aes = Aes.Create())
            {
                aes.Key = _encryptionKey;

                // اندازه IV (پیش‌فرض 16 بایت برای AES)
                int ivSize = aes.BlockSize / 8;

                // یادآوری: IV در ابتدای داده‌ها ذخیره شده است
                byte[] iv = new byte[ivSize];
                Array.Copy(encryptedBytes, 0, iv, 0, ivSize);

                using (ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, iv))
                {
                    using (MemoryStream ms = new MemoryStream())
                    {
                        using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Write))
                        {
                            // داده‌ها را بدون IV رمزگشایی می‌کنیم
                            cs.Write(encryptedBytes, ivSize, encryptedBytes.Length - ivSize);
                        }

                        return Encoding.UTF8.GetString(ms.ToArray());
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Decryption error: {e.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// دریافت زمان باقی‌مانده تا انقضای نشست
    /// </summary>
    public TimeSpan GetSessionTimeRemaining()
    {
        if (!IsLoggedIn)
            return TimeSpan.Zero;

        TimeSpan elapsed = DateTime.Now - _lastActivityTime;
        TimeSpan remaining = TimeSpan.FromMinutes(_sessionTimeoutMinutes) - elapsed;

        return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
    }

    /// <summary>
    /// افزایش زمان انقضای نشست
    /// </summary>
    public void ExtendSession()
    {
        RecordActivity();
        Debug.Log("Session extended");
    }

    /// <summary>
    /// غیرفعال کردن مدیریت نشست
    /// </summary>
    private void OnDestroy()
    {
        // اطمینان از ذخیره شدن داده‌ها قبل از غیرفعال شدن
        if (IsLoggedIn)
        {
            SaveSessionData();
        }
    }

    /// <summary>
    /// وضعیت سلامت نشست را گزارش می‌دهد
    /// </summary>
    public string GetSessionHealthReport()
    {
        if (!IsLoggedIn)
            return "Not logged in";

        StringBuilder report = new StringBuilder();
        report.AppendLine($"User: {Username}");
        report.AppendLine($"Session active: {IsLoggedIn}");
        report.AppendLine($"Last activity: {_lastActivityTime}");
        report.AppendLine($"Time remaining: {GetSessionTimeRemaining().TotalMinutes:0.0} minutes");
        report.AppendLine($"Using encryption: {_useEncryption}");
        report.AppendLine($"Using PlayerPrefs fallback: {_usePlayerPrefs}");
        report.AppendLine($"Secure storage file exists: {File.Exists(_dataFilePath)}");

        return report.ToString();
    }
}