using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using TMPro;
using UnityEngine.UI;
using System.Text;
using Newtonsoft.Json;
using UnityEngine.SceneManagement;

public class Register : MonoBehaviour
{
    [SerializeField] private TMP_InputField usernameInput;
    [SerializeField] private TMP_InputField emailInput;
    [SerializeField] private TMP_InputField passwordInput;
    [SerializeField] private TMP_InputField confirmPasswordInput;
    [SerializeField] private TextMeshProUGUI passwordMatchText; // متن نمایش وضعیت تطابق رمز عبور
    [SerializeField] private TMP_InputField walletAddressInput;
    [SerializeField] private TextMeshProUGUI walletAddressValidationText; // متن نمایش وضعیت اعتبار آدرس کیف پول
    [SerializeField] private TMP_InputField referralCodeInput;
    [SerializeField] private TextMeshProUGUI referralCodeValidationText; // متن نمایش وضعیت اعتبار کد رفرال
    [SerializeField] private Button checkReferralCodeButton; // دکمه برای بررسی اعتبار کد رفرال
    [SerializeField] private Button registerButton;
    [SerializeField] private Button pasteWalletButton; // دکمه برای پیست کردن آدرس کیف پول
    [SerializeField] private Button pasteReferralCodeButton; // دکمه برای پیست کردن کد معرف
    [SerializeField] private Button togglePasswordButton; // دکمه برای نمایش/مخفی کردن هر دو فیلد رمز عبور
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/register.php";
    [SerializeField] private string checkReferralCodeUrl = "https://game-gofaster.com/gam/api/check_referral_code.php";

    [SerializeField] private GameObject sessionManagerPrefab;

    private void Awake()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
        }
    }

    void Start()
    {
        if (registerButton != null)
        {
            registerButton.onClick.AddListener(OnRegisterButtonClick);
        }

        // اضافه کردن لیسنر برای دکمه پیست آدرس کیف پول
        if (pasteWalletButton != null)
        {
            pasteWalletButton.onClick.AddListener(OnPasteWalletButtonClick);
        }

        // اضافه کردن لیسنر برای دکمه پیست کد معرف
        if (pasteReferralCodeButton != null)
        {
            pasteReferralCodeButton.onClick.AddListener(OnPasteReferralCodeButtonClick);
        }

        // اضافه کردن لیسنر برای دکمه بررسی اعتبار کد رفرال
        if (checkReferralCodeButton != null)
        {
            checkReferralCodeButton.onClick.AddListener(OnCheckReferralCodeButtonClick);
        }

        // پنهان کردن متن اعتبارسنجی کد رفرال در ابتدا
        if (referralCodeValidationText != null)
        {
            referralCodeValidationText.gameObject.SetActive(false);
        }

        // اضافه کردن لیسنر برای دکمه نمایش/مخفی کردن رمز عبور
        if (togglePasswordButton != null)
        {
            togglePasswordButton.onClick.AddListener(OnTogglePasswordButtonClick);
        }

        // اضافه کردن لیسنر برای تغییر متن فیلدهای رمز عبور
        if (passwordInput != null && confirmPasswordInput != null)
        {
            passwordInput.onValueChanged.AddListener(OnPasswordChanged);
            confirmPasswordInput.onValueChanged.AddListener(OnConfirmPasswordChanged);

            // پنهان کردن متن تطابق رمز عبور در ابتدا
            if (passwordMatchText != null)
            {
                passwordMatchText.gameObject.SetActive(false);
            }
        }

        // اضافه کردن لیسنر برای تغییر متن فیلد آدرس کیف پول
        if (walletAddressInput != null)
        {
            walletAddressInput.onValueChanged.AddListener(OnWalletAddressChanged);

            // پنهان کردن متن اعتبارسنجی در ابتدا
            if (walletAddressValidationText != null)
            {
                walletAddressValidationText.gameObject.SetActive(false);
            }
        }

        // Check if we have required input fields
        if (usernameInput == null || emailInput == null || passwordInput == null)
        {
            Debug.LogError("Register UI is missing required input fields!");
            if (statusText != null) statusText.text = "UI Configuration Error";
        }
    }

    public void OnRegisterButtonClick()
    {
        if (statusText != null) statusText.text = "Registering...";

        // Validate input fields
        if (usernameInput.text.Length < 3 || usernameInput.text.Length > 20)
        {
            if (statusText != null) statusText.text = "Username must be 3-20 characters";
            return;
        }

        if (string.IsNullOrEmpty(emailInput.text) || !IsValidEmail(emailInput.text))
        {
            if (statusText != null) statusText.text = "Please enter a valid email";
            return;
        }

        if (passwordInput.text.Length < 8)
        {
            if (statusText != null) statusText.text = "Password must be at least 8 characters";
            return;
        }

        // بررسی تطابق رمز عبور و تأیید رمز عبور
        if (confirmPasswordInput != null && passwordInput.text != confirmPasswordInput.text)
        {
            if (statusText != null) statusText.text = "Passwords do not match";
            return;
        }

        // Optional validation for wallet address format
        if (!string.IsNullOrEmpty(walletAddressInput.text) && !IsValidWalletAddress(walletAddressInput.text))
        {
            if (statusText != null) statusText.text = "Invalid wallet address format";
            return;
        }

        // اگر کد رفرال وارد شده است، ابتدا اعتبار آن را بررسی کنیم
        if (referralCodeInput != null && !string.IsNullOrEmpty(referralCodeInput.text))
        {
            StartCoroutine(CheckReferralCodeValidity());
        }
        else
        {
            // اگر کد رفرال وارد نشده است، مستقیماً ثبت‌نام را انجام دهیم
            StartCoroutine(RegisterCoroutine());
        }
    }

    /// <summary>
    /// بررسی اعتبار کد رفرال
    /// </summary>
    private IEnumerator CheckReferralCodeValidity()
    {
        if (statusText != null) statusText.text = "Checking referral code...";

        // ساخت داده‌های درخواست
        Dictionary<string, string> requestData = new Dictionary<string, string>
        {
            { "referral_code", referralCodeInput.text }
        };

        string jsonData = JsonConvert.SerializeObject(requestData);

        using (UnityWebRequest request = new UnityWebRequest(checkReferralCodeUrl, "POST"))
        {
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");

            yield return request.SendWebRequest();

            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Referral code check failed: {request.error}");
                if (statusText != null) statusText.text = $"Error checking referral code: {request.error}";
            }
            else
            {
                Debug.Log($"Referral code check response: {request.downloadHandler.text}");

                try
                {
                    var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(request.downloadHandler.text);

                    if (response.ContainsKey("status") && response["status"].ToString() == "success")
                    {
                        // کد رفرال معتبر است، ادامه ثبت‌نام
                        StartCoroutine(RegisterCoroutine());
                    }
                    else
                    {
                        // کد رفرال نامعتبر است
                        string errorMessage = "Invalid referral code";
                        if (response.ContainsKey("message"))
                        {
                            errorMessage = response["message"].ToString();
                        }

                        Debug.LogError($"Referral code check failed: {errorMessage}");
                        if (statusText != null) statusText.text = errorMessage;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error parsing referral code check response: {e.Message}");
                    if (statusText != null) statusText.text = "Error processing server response";
                }
            }
        }
    }

    private IEnumerator RegisterCoroutine()
    {
        string username = usernameInput.text;
        string email = emailInput.text;
        string password = passwordInput.text;
        string walletAddress = walletAddressInput ? walletAddressInput.text : "";
        string referralCode = referralCodeInput ? referralCodeInput.text : "";

        // Create registration data
        Dictionary<string, string> registrationData = new Dictionary<string, string>
        {
            { "username", username },
            { "email", email },
            { "password", password }
        };

        // Add wallet address if provided
        if (!string.IsNullOrEmpty(walletAddress))
        {
            registrationData.Add("wallet_address", walletAddress);
        }

        // Add referral code if provided
        if (!string.IsNullOrEmpty(referralCode))
        {
            registrationData.Add("referral_code", referralCode);
        }

        string jsonData = JsonConvert.SerializeObject(registrationData);

        using (UnityWebRequest request = new UnityWebRequest(apiUrl, "POST"))
        {
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");

            yield return request.SendWebRequest();

            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Registration failed: {request.error}");
                if (statusText != null) statusText.text = $"Registration error: {request.error}";
            }
            else
            {
                Debug.Log($"Registration response: Code={request.responseCode}, Text={request.downloadHandler.text}");

                try
                {
                    var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(request.downloadHandler.text);

                    if (response.ContainsKey("status") && response["status"].ToString() == "success")
                    {
                        // Successfully registered
                        Debug.Log("Registration successful");
                        if (statusText != null) statusText.text = "Registration successful!";

                        // Store auth data
                        if (response.ContainsKey("token"))
                        {
                            PlayerPrefs.SetString("Token", response["token"].ToString());

                            // Store token expiry time if available
                            if (response.ContainsKey("token_expires_in"))
                            {
                                int expiresIn = Convert.ToInt32(response["token_expires_in"]);
                                // Store expiry timestamp (current time + expires_in seconds)
                                long expiryTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds() + expiresIn;
                                PlayerPrefs.SetString("TokenExpiry", expiryTimestamp.ToString());
                                Debug.Log($"Token expires in {expiresIn} seconds (at {expiryTimestamp})");
                            }

                            // Store token type if available
                            if (response.ContainsKey("token_type"))
                            {
                                PlayerPrefs.SetString("TokenType", response["token_type"].ToString());
                            }
                        }

                        if (response.ContainsKey("key1"))
                        {
                            PlayerPrefs.SetString("Key1", response["key1"].ToString());
                        }

                        if (response.ContainsKey("nonce"))
                        {
                            PlayerPrefs.SetString("Nonce", response["nonce"].ToString());
                        }
                    }
                    else
                    {
                        // Registration failed
                        string errorMessage = "Registration failed";
                        if (response.ContainsKey("message"))
                        {
                            errorMessage = response["message"].ToString();
                        }

                        Debug.LogError($"Registration failed: {errorMessage}");
                        if (statusText != null) statusText.text = errorMessage;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error parsing registration response: {e.Message}");
                    if (statusText != null) statusText.text = "Error processing server response";
                }
            }
        }
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    // متد برای پیست کردن آدرس کیف پول از کلیپ‌بورد
    public void OnPasteWalletButtonClick()
    {
        if (walletAddressInput != null)
        {
            // دریافت متن از کلیپ‌بورد
            string clipboardText = GUIUtility.systemCopyBuffer;

            // بررسی اینکه آیا متن کلیپ‌بورد خالی نیست
            if (!string.IsNullOrEmpty(clipboardText))
            {
                // قرار دادن متن در فیلد آدرس کیف پول
                walletAddressInput.text = clipboardText;

                // اعتبارسنجی آدرس کیف پول با استفاده از متد OnWalletAddressChanged
                // این متد به طور خودکار پیام‌های مناسب را نمایش می‌دهد
                OnWalletAddressChanged(clipboardText);

                // نمایش پیام موفقیت در statusText
                if (statusText != null)
                {
                    if (IsValidWalletAddress(clipboardText))
                    {
                        statusText.text = "Valid wallet address pasted successfully";
                    }
                    else
                    {
                        statusText.text = "Warning: The pasted wallet address may not be valid";
                    }

                    // پاک کردن پیام پس از 2 ثانیه
                    StartCoroutine(ClearStatusAfterDelay(2.0f));
                }
            }
            else
            {
                if (statusText != null)
                {
                    statusText.text = "Clipboard is empty";
                }
            }
        }
    }

    // متد برای نمایش/مخفی کردن هر دو فیلد رمز عبور
    public void OnTogglePasswordButtonClick()
    {
        // بررسی وجود فیلدهای رمز عبور
        if (passwordInput == null || confirmPasswordInput == null)
            return;

        // تعیین وضعیت جدید بر اساس وضعیت فعلی فیلد رمز عبور
        TMP_InputField.ContentType newContentType = (passwordInput.contentType == TMP_InputField.ContentType.Password)
            ? TMP_InputField.ContentType.Standard
            : TMP_InputField.ContentType.Password;

        // تغییر وضعیت نمایش هر دو فیلد رمز عبور
        passwordInput.contentType = newContentType;
        confirmPasswordInput.contentType = newContentType;

        // به‌روزرسانی فیلدهای ورودی
        passwordInput.ForceLabelUpdate();
        confirmPasswordInput.ForceLabelUpdate();

        // بررسی مجدد تطابق رمز عبور
        CheckPasswordsMatch();

        // نمایش پیام وضعیت (اختیاری)
        if (statusText != null)
        {
            statusText.text = (newContentType == TMP_InputField.ContentType.Standard)
                ? "Password visible"
                : "Password hidden";

            // پاک کردن پیام پس از 2 ثانیه
            StartCoroutine(ClearStatusAfterDelay(2.0f));
        }
    }

    // متد برای پیست کردن کد معرف از کلیپ‌بورد
    public void OnPasteReferralCodeButtonClick()
    {
        if (referralCodeInput != null)
        {
            // دریافت متن از کلیپ‌بورد
            string clipboardText = GUIUtility.systemCopyBuffer;

            // بررسی اینکه آیا متن کلیپ‌بورد خالی نیست
            if (!string.IsNullOrEmpty(clipboardText))
            {
                // قرار دادن متن در فیلد کد معرف
                referralCodeInput.text = clipboardText;

                if (statusText != null)
                {
                    statusText.text = "Referral code pasted successfully";
                    // پاک کردن پیام پس از 2 ثانیه
                    StartCoroutine(ClearStatusAfterDelay(2.0f));
                }
            }
            else
            {
                if (statusText != null)
                {
                    statusText.text = "Clipboard is empty";
                    // پاک کردن پیام پس از 2 ثانیه
                    StartCoroutine(ClearStatusAfterDelay(2.0f));
                }
            }
        }
    }

    // متد برای بررسی اعتبار کد رفرال
    public void OnCheckReferralCodeButtonClick()
    {
        if (referralCodeInput == null || string.IsNullOrEmpty(referralCodeInput.text))
        {
            if (statusText != null)
            {
                statusText.text = "Please enter a referral code first";
                StartCoroutine(ClearStatusAfterDelay(2.0f));
            }
            return;
        }

        // شروع کوروتین بررسی اعتبار کد رفرال
        StartCoroutine(CheckReferralCodeValidityUI());
    }

    /// <summary>
    /// بررسی اعتبار کد رفرال برای نمایش در رابط کاربری
    /// </summary>
    private IEnumerator CheckReferralCodeValidityUI()
    {
        if (statusText != null) statusText.text = "Checking referral code...";

        // پنهان کردن متن اعتبارسنجی کد رفرال
        if (referralCodeValidationText != null)
        {
            referralCodeValidationText.gameObject.SetActive(false);
        }

        // ساخت داده‌های درخواست
        Dictionary<string, string> requestData = new Dictionary<string, string>
        {
            { "referral_code", referralCodeInput.text }
        };

        string jsonData = JsonConvert.SerializeObject(requestData);

        using (UnityWebRequest request = new UnityWebRequest(checkReferralCodeUrl, "POST"))
        {
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");

            yield return request.SendWebRequest();

            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Referral code check failed: {request.error}");
                if (statusText != null) statusText.text = $"Error checking referral code: {request.error}";
            }
            else
            {
                Debug.Log($"Referral code check response: {request.downloadHandler.text}");

                try
                {
                    var response = JsonConvert.DeserializeObject<Dictionary<string, object>>(request.downloadHandler.text);

                    // نمایش متن اعتبارسنجی کد رفرال
                    if (referralCodeValidationText != null)
                    {
                        referralCodeValidationText.gameObject.SetActive(true);

                        if (response.ContainsKey("status") && response["status"].ToString() == "success")
                        {
                            // کد رفرال معتبر است
                            referralCodeValidationText.text = "Valid referral code";
                            referralCodeValidationText.color = Color.green;

                            if (statusText != null)
                            {
                                statusText.text = "Referral code is valid";
                                StartCoroutine(ClearStatusAfterDelay(2.0f));
                            }
                        }
                        else
                        {
                            // کد رفرال نامعتبر است
                            string errorMessage = "Invalid referral code";
                            if (response.ContainsKey("message"))
                            {
                                errorMessage = response["message"].ToString();
                            }

                            referralCodeValidationText.text = "Invalid referral code";
                            referralCodeValidationText.color = Color.red;

                            if (statusText != null)
                            {
                                statusText.text = errorMessage;
                                StartCoroutine(ClearStatusAfterDelay(2.0f));
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error parsing referral code check response: {e.Message}");
                    if (statusText != null)
                    {
                        statusText.text = "Error processing server response";
                        StartCoroutine(ClearStatusAfterDelay(2.0f));
                    }
                }
            }
        }
    }

    // متد برای پاک کردن پیام وضعیت پس از مدت زمان مشخص
    private IEnumerator ClearStatusAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        if (statusText != null)
            statusText.text = "";
    }

    /// <summary>
    /// متد برای بررسی تطابق رمز عبور در زمان واقعی (هنگام تغییر رمز عبور اصلی)
    /// </summary>
    public void OnPasswordChanged(string password)
    {
        CheckPasswordsMatch();
    }

    /// <summary>
    /// متد برای بررسی تطابق رمز عبور در زمان واقعی (هنگام تغییر تأیید رمز عبور)
    /// </summary>
    public void OnConfirmPasswordChanged(string confirmPassword)
    {
        CheckPasswordsMatch();
    }

    /// <summary>
    /// متد برای بررسی تطابق رمز عبور و تأیید رمز عبور
    /// </summary>
    private void CheckPasswordsMatch()
    {
        if (passwordMatchText == null || passwordInput == null || confirmPasswordInput == null)
            return;

        // اگر هر دو فیلد خالی هستند، متن تطابق را پنهان کن
        if (string.IsNullOrEmpty(passwordInput.text) && string.IsNullOrEmpty(confirmPasswordInput.text))
        {
            passwordMatchText.gameObject.SetActive(false);
            return;
        }

        // اگر یکی از فیلدها خالی است، متن تطابق را پنهان کن
        if (string.IsNullOrEmpty(passwordInput.text) || string.IsNullOrEmpty(confirmPasswordInput.text))
        {
            passwordMatchText.gameObject.SetActive(false);
            return;
        }

        // نمایش متن تطابق
        passwordMatchText.gameObject.SetActive(true);

        // بررسی تطابق رمز عبور
        if (passwordInput.text == confirmPasswordInput.text)
        {
            passwordMatchText.text = "Passwords match";
            // تبدیل کد رنگ هگزادسیمال به Color در Unity
            Color customColor;
            ColorUtility.TryParseHtmlString("#EFFF99A4", out customColor);
            passwordMatchText.color = customColor;
        }
        else
        {
            passwordMatchText.text = "Passwords do not match";
            passwordMatchText.color = Color.red;
        }
    }

    /// <summary>
    /// متد برای اعتبارسنجی آدرس کیف پول در زمان واقعی
    /// </summary>
    public void OnWalletAddressChanged(string address)
    {
        if (walletAddressValidationText == null)
            return;

        // اگر آدرس خالی است، متن اعتبارسنجی را پنهان کن
        if (string.IsNullOrEmpty(address))
        {
            walletAddressValidationText.gameObject.SetActive(false);
            return;
        }

        // نمایش متن اعتبارسنجی
        walletAddressValidationText.gameObject.SetActive(true);

        // بررسی اعتبار آدرس
        if (address.Length < 42) // هنوز کامل نیست
        {
            if (address.StartsWith("0x"))
            {
                walletAddressValidationText.text = "Keep typing... (need 42 characters total)";
                walletAddressValidationText.color = new Color(1f, 0.5f, 0f); // نارنجی
            }
            else
            {
                walletAddressValidationText.text = "Address must start with 0x";
                walletAddressValidationText.color = Color.red;
            }
        }
        else if (IsValidWalletAddress(address))
        {
            walletAddressValidationText.text = "Valid wallet address";
            walletAddressValidationText.color = Color.green;
        }
        else
        {
            walletAddressValidationText.text = "Invalid wallet address format";
            walletAddressValidationText.color = Color.red;
        }
    }

    /// <summary>
    /// بررسی اعتبار آدرس کیف پول
    /// آدرس کیف پول معتبر باید با 0x شروع شود و شامل 40 کاراکتر هگزادسیمال باشد
    /// این متد همچنین بررسی می‌کند که آیا آدرس شامل حروف کوچک و بزرگ مخلوط است
    /// </summary>
    private bool IsValidWalletAddress(string address)
    {
        // بررسی اولیه: آیا آدرس با 0x شروع می‌شود و شامل 42 کاراکتر است (0x + 40 کاراکتر هگزادسیمال)
        if (!System.Text.RegularExpressions.Regex.IsMatch(address, @"^0x[a-fA-F0-9]{40}$"))
            return false;

        // بررسی اضافی: آیا آدرس شامل حروف کوچک و بزرگ مخلوط است
        // این بررسی اختیاری است و می‌تواند حذف شود اگر می‌خواهید کمتر سخت‌گیرانه باشد
        bool hasLower = System.Text.RegularExpressions.Regex.IsMatch(address, "[a-f]");
        bool hasUpper = System.Text.RegularExpressions.Regex.IsMatch(address, "[A-F]");

        // اگر هم حروف کوچک و هم حروف بزرگ دارد، ممکن است آدرس نامعتبر باشد
        // اما این بررسی را نادیده می‌گیریم چون گفتید زیاد سخت‌گیرانه نباشد

        return true;
    }

    [Serializable]
    private class RegisterResponse
    {
        public string status;
        public string message;
        public string token;
        public string key1;
        public string nonce;
    }
}