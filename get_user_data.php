<?php
/**
 * Get User Data API
 *
 * This API fetches user data including owned cars
 * All operations require JWT token authentication
 */

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', 'user_data_errors.log');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Helper function to safely log debug info (disabled)
function debug_log($message) {
    // Commenting out the logging to prevent file growth
    // error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, 'user_data_debug.log');
    return;
}

/**
 * Send a JSON response
 *
 * @param array $data The data to send
 * @param int $status_code The HTTP status code
 */
if (!function_exists('respondJSON')) {
    function respondJSON($data, $status_code = 200) {
        http_response_code($status_code);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// Helper function to get the token
if (!function_exists('getBearerToken')) {
    function getBearerToken() {
        $headers = null;
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }

        if (!empty($headers) && preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
        return null;
    }
}

// اجرای کل کد در یک بلوک try-catch برای مدیریت خطاها
try {
    // Start debugging
    debug_log("API request received: " . json_encode($_GET));

    // Check if this is a car status check request
    if (isset($_GET['check_car'])) {
        $car_id = $_GET['check_car'];
        debug_log("Car status check request for car: $car_id");

        // Get authorization token
        $token = getBearerToken();
        debug_log("Token received: " . ($token ? "Yes" : "No"));

        if (!$token) {
            respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
        }

        // Verify token
        $user_id = verifyJWT($token);
        debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

        if (!$user_id) {
            respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
        }

        // If somehow user_id is still an array, extract the ID
        if (is_array($user_id) && isset($user_id['user_id'])) {
            $user_id = $user_id['user_id'];
            debug_log("Extracted user_id from array: " . $user_id);
        }

        // Check if the user owns the car and if it's activated
        $stmt = $conn->prepare("SELECT is_active FROM user_cars WHERE user_id = ? AND car_id = ?");
        $stmt->bind_param('is', $user_id, $car_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $is_activated = (bool)$row['is_active'];

            respondJSON([
                'status' => 'success',
                'car_status' => [
                    'is_owned' => true,
                    'is_activated' => $is_activated
                ]
            ]);
        } else {
            // User doesn't own this car
            respondJSON([
                'status' => 'success',
                'car_status' => [
                    'is_owned' => false,
                    'is_activated' => false
                ]
            ]);
        }

        // Exit early as we've sent the response
        exit;
    }

    // This is a regular user data request, continue with the normal flow
    // Get key1 from query parameters (with 24-hour token system, only key1 is required)
    if (!isset($_GET['key1'])) {
        respondJSON(['status' => 'error', 'message' => 'Key1 is required'], 400);
    }

    $key1 = $_GET['key1'];
    // With 24-hour token system, nonce is optional
    $nonce = isset($_GET['nonce']) ? $_GET['nonce'] : '';

    // Check if this is a refresh_nonce request
    $refresh_nonce = isset($_GET['refresh_nonce']) && $_GET['refresh_nonce'] == '1';

    debug_log("Parameters: key1=" . substr($key1, 0, 5) . "..., nonce=" . $nonce);

    // Get authorization token
    $token = getBearerToken();
    debug_log("Token received: " . ($token ? "Yes" : "No"));

    if (!$token) {
        respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
    }

    // Verify token
    $user_id = verifyJWT($token);
    debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

    if (!$user_id) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
    }

    // If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
    if (is_array($user_id) && isset($user_id['user_id'])) {
        $user_id = $user_id['user_id'];
        debug_log("Extracted user_id from array: " . $user_id);
    }

    // Check rate limit
    if (!checkRateLimit($user_id)) {
        respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
    }

    // Verify Key1
    if (!verifyUserKey($user_id, $key1)) {
        respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
    }

    // Verify Nonce if provided (with 24-hour token system, nonce is optional)
    if (!empty($nonce)) {
        if (!verifyAndUseNonce($user_id, $nonce)) {
            // Generate a new nonce for the client to use
            $new_nonce = generateNonce($user_id);

            // Return error with the new nonce
            respondJSON([
                'status' => 'error',
                'message' => 'Invalid or used nonce',
                'should_retry' => true,
                'error_code' => 'INVALID_NONCE',
                'nonce' => $new_nonce
            ], 401);
        }
    } else {
        // With 24-hour token system, we can proceed without a nonce
        debug_log("No nonce provided, proceeding with 24-hour token system");
    }

    // Generate a new nonce for the next request
    $new_nonce = generateNonce($user_id);
    debug_log("New nonce generated: $new_nonce");

    // Fetch user data
    debug_log("Fetching user data for user ID: $user_id");

    // Get user data
    $stmt = $conn->prepare("SELECT u.username, u.email, u.wallet_address, u.balance, u.win_balance, u.referral_balance,
                           IFNULL(gs.games_count, 0) as games_count,
                           IFNULL(gs.wins_count, 0) as wins_count,
                           IFNULL(gs.losses_count, 0) as losses_count
                           FROM users u
                           LEFT JOIN game_stats gs ON u.id = gs.user_id
                           WHERE u.id = ?");

    if (!$stmt) {
        debug_log("SQL preparation error: " . $conn->error);
        throw new Exception("Database error: " . $conn->error);
    }

    $stmt->bind_param('i', $user_id);
    $result = $stmt->execute();

    if (!$result) {
        debug_log("SQL execution error: " . $stmt->error);
        throw new Exception("Database error: " . $stmt->error);
    }

    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        debug_log("User not found");
        respondJSON(['status' => 'error', 'message' => 'User not found', 'nonce' => $new_nonce], 404);
    }

    $user = $result->fetch_assoc();
    debug_log("User data fetched: " . json_encode($user));

    // Get user's cars from the user_cars table
    $stmt = $conn->prepare("SELECT id, user_id, car_id, is_active as is_activated, purchase_date FROM user_cars WHERE user_id = ?");
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $cars_result = $stmt->get_result();

    // Old format (for backward compatibility)
    $cars_data = [];

    // New format - array of car objects
    $user_cars = [];

    while ($car = $cars_result->fetch_assoc()) {
        // Old format - Change from "Car:1 1" to a proper format that Unity expects
        $cars_data[] = $car['car_id'] . ' ' . $car['is_activated'];

        // New format
        $user_cars[] = [
            'id' => (int)$car['id'],
            'user_id' => $car['user_id'],
            'car_id' => $car['car_id'],
            'is_activated' => (int)$car['is_activated'],
            'purchase_date' => $car['purchase_date']
        ];
    }

    debug_log("User cars (old format): " . json_encode($cars_data));
    debug_log("User cars (new format): " . json_encode($user_cars));

    // Generate a unique encryption key for this session
    $unique_seed = $user_id . time() . bin2hex(random_bytes(8));
    $encryption_key = hash('sha256', $unique_seed);

    // Generate a key that's exactly 32 bytes (256 bits) for AES-256
    $short_encryption_key = substr($encryption_key, 0, 32);

    // Return user data with owned cars
    $response = [
        'status' => 'success',
        'user' => [
            'username' => $user['username'],
            'email' => $user['email'],
            'wallet_address' => $user['wallet_address'],
            'balance' => (float)$user['balance'],
            'win_balance' => (float)$user['win_balance'],
            'referral_balance' => (float)$user['referral_balance'],
            'games_count' => (int)$user['games_count'],
            'wins_count' => (int)$user['wins_count'],
            'losses_count' => (int)$user['losses_count'],
            'cars_data' => $cars_data,
            'user_cars' => $user_cars
        ],
        'encryption_key' => $short_encryption_key,
        'nonce' => $new_nonce
    ];

    // تغییر روش لاگ کردن داده‌ها برای جلوگیری از خطای تبدیل آرایه به رشته
    $response_debug = json_encode($response);
    debug_log("Sending response: " . $response_debug);
    respondJSON($response);

} catch (Exception $e) {
    debug_log("CRITICAL ERROR in get_user_data: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString());
    respondJSON([
        'status' => 'error',
        'message' => 'Server error: ' . $e->getMessage(),
        'nonce' => isset($new_nonce) ? $new_nonce : generateNonce($user_id)
    ], 500);
}
?>